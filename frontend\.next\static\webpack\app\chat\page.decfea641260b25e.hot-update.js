"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/StreamingMarkdownRenderer.tsx":
/*!***************************************************************!*\
  !*** ./src/app/chat/components/StreamingMarkdownRenderer.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMarkdownRenderer: () => (/* binding */ StreamingMarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _MarkdownRenderer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MarkdownRenderer */ \"(app-pages-browser)/./src/app/chat/components/MarkdownRenderer.tsx\");\n/* __next_internal_client_entry_do_not_use__ StreamingMarkdownRenderer auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction StreamingMarkdownRenderer(param) {\n    let { content, isStreaming = false, className = '' } = param;\n    _s();\n    const [displayContent, setDisplayContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [showCursor, setShowCursor] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const lastContentLength = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n    // 处理内容更新\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n            setDisplayContent(content);\n            lastContentLength.current = content.length;\n        }\n    }[\"StreamingMarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    // 处理流式状态的光标显示\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n            setShowCursor(isStreaming);\n        }\n    }[\"StreamingMarkdownRenderer.useEffect\"], [\n        isStreaming\n    ]);\n    // 自动滚动到新内容\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n            if (contentRef.current && content.length > lastContentLength.current) {\n                const element = contentRef.current;\n                const isNearBottom = element.scrollTop + element.clientHeight >= element.scrollHeight - 100;\n                if (isNearBottom) {\n                    setTimeout({\n                        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n                            element.scrollTop = element.scrollHeight;\n                        }\n                    }[\"StreamingMarkdownRenderer.useEffect\"], 50);\n                }\n            }\n        }\n    }[\"StreamingMarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: contentRef,\n        className: \"jsx-28be04fc9c921754\" + \" \" + \"streaming-markdown-container relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-28be04fc9c921754\" + \" \" + \"flex-1 min-w-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_3__.MarkdownRenderer, {\n                    content: displayContent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            showCursor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    animation: isStreaming ? 'pulse 1.2s infinite' : 'none'\n                },\n                className: \"jsx-28be04fc9c921754\" + \" \" + \"inline-block w-2 h-5 bg-blue-500 ml-1 transition-opacity duration-150 \".concat(showCursor ? 'opacity-100' : 'opacity-0')\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"28be04fc9c921754\",\n                children: \"@-webkit-keyframes pulse{0%,50%{opacity:1}51%,100%{opacity:.3}}@-moz-keyframes pulse{0%,50%{opacity:1}51%,100%{opacity:.3}}@-o-keyframes pulse{0%,50%{opacity:1}51%,100%{opacity:.3}}@keyframes pulse{0%,50%{opacity:1}51%,100%{opacity:.3}}.streaming-markdown-container.jsx-28be04fc9c921754{overflow-anchor:auto}.streaming-markdown-container.jsx-28be04fc9c921754 *{-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-font-smoothing:antialiased}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(StreamingMarkdownRenderer, \"BHP1QmQcn10gh5OvM1ODSa38FgA=\");\n_c = StreamingMarkdownRenderer;\nvar _c;\n$RefreshReg$(_c, \"StreamingMarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/StreamingMarkdownRenderer.tsx\n"));

/***/ })

});