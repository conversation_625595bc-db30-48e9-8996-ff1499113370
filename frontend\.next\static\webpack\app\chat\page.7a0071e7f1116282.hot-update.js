"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/StreamingMarkdownRenderer.tsx":
/*!***************************************************************!*\
  !*** ./src/app/chat/components/StreamingMarkdownRenderer.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMarkdownRenderer: () => (/* binding */ StreamingMarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarkdownRenderer */ \"(app-pages-browser)/./src/app/chat/components/MarkdownRenderer.tsx\");\n/* __next_internal_client_entry_do_not_use__ StreamingMarkdownRenderer auto */ \nvar _s = $RefreshSig$();\n\n\nfunction StreamingMarkdownRenderer(param) {\n    let { content, isStreaming = false, className = '' } = param;\n    _s();\n    const [displayContent, setDisplayContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showCursor, setShowCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const lastContentLength = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    // 处理内容更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n            setDisplayContent(content);\n            lastContentLength.current = content.length;\n        }\n    }[\"StreamingMarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    // 处理流式状态的光标显示\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n            setShowCursor(isStreaming);\n        }\n    }[\"StreamingMarkdownRenderer.useEffect\"], [\n        isStreaming\n    ]);\n    // 移除自动滚动功能，避免干扰\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"streaming-markdown-container \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__.MarkdownRenderer, {\n                content: displayContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            showCursor && isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-block w-0.5 h-4 bg-blue-500 ml-1 animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(StreamingMarkdownRenderer, \"i0GzM5ClE6+Hb/6QcB70c6eSJeg=\");\n_c = StreamingMarkdownRenderer;\nvar _c;\n$RefreshReg$(_c, \"StreamingMarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY2hhdC9jb21wb25lbnRzL1N0cmVhbWluZ01hcmtkb3duUmVuZGVyZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFMkQ7QUFDTDtBQVEvQyxTQUFTSywwQkFBMEIsS0FJVDtRQUpTLEVBQ3hDQyxPQUFPLEVBQ1BDLGNBQWMsS0FBSyxFQUNuQkMsWUFBWSxFQUFFLEVBQ2lCLEdBSlM7O0lBS3hDLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR1IsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDUyxZQUFZQyxjQUFjLEdBQUdWLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU1XLGFBQWFWLDZDQUFNQSxDQUFpQjtJQUMxQyxNQUFNVyxvQkFBb0JYLDZDQUFNQSxDQUFDO0lBRWpDLFNBQVM7SUFDVEYsZ0RBQVNBOytDQUFDO1lBQ1JTLGtCQUFrQko7WUFDbEJRLGtCQUFrQkMsT0FBTyxHQUFHVCxRQUFRVSxNQUFNO1FBQzVDOzhDQUFHO1FBQUNWO0tBQVE7SUFFWixjQUFjO0lBQ2RMLGdEQUFTQTsrQ0FBQztZQUNSVyxjQUFjTDtRQUNoQjs4Q0FBRztRQUFDQTtLQUFZO0lBRWhCLGdCQUFnQjtJQUVoQixxQkFDRSw4REFBQ1U7UUFBSVQsV0FBVyxnQ0FBMEMsT0FBVkE7OzBCQUU5Qyw4REFBQ0osK0RBQWdCQTtnQkFBQ0UsU0FBU0c7Ozs7OztZQUcxQkUsY0FBY0osNkJBQ2IsOERBQUNXO2dCQUFLVixXQUFVOzs7Ozs7Ozs7Ozs7QUFJeEI7R0FsQ2dCSDtLQUFBQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcc3JjXFxhcHBcXGNoYXRcXGNvbXBvbmVudHNcXFN0cmVhbWluZ01hcmtkb3duUmVuZGVyZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUsIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IE1hcmtkb3duUmVuZGVyZXIgfSBmcm9tICcuL01hcmtkb3duUmVuZGVyZXInO1xuXG5pbnRlcmZhY2UgU3RyZWFtaW5nTWFya2Rvd25SZW5kZXJlclByb3BzIHtcbiAgY29udGVudDogc3RyaW5nO1xuICBpc1N0cmVhbWluZz86IGJvb2xlYW47XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFN0cmVhbWluZ01hcmtkb3duUmVuZGVyZXIoeyBcbiAgY29udGVudCwgXG4gIGlzU3RyZWFtaW5nID0gZmFsc2UsIFxuICBjbGFzc05hbWUgPSAnJyBcbn06IFN0cmVhbWluZ01hcmtkb3duUmVuZGVyZXJQcm9wcykge1xuICBjb25zdCBbZGlzcGxheUNvbnRlbnQsIHNldERpc3BsYXlDb250ZW50XSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3Nob3dDdXJzb3IsIHNldFNob3dDdXJzb3JdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBjb250ZW50UmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcbiAgY29uc3QgbGFzdENvbnRlbnRMZW5ndGggPSB1c2VSZWYoMCk7XG5cbiAgLy8g5aSE55CG5YaF5a655pu05pawXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0RGlzcGxheUNvbnRlbnQoY29udGVudCk7XG4gICAgbGFzdENvbnRlbnRMZW5ndGguY3VycmVudCA9IGNvbnRlbnQubGVuZ3RoO1xuICB9LCBbY29udGVudF0pO1xuXG4gIC8vIOWkhOeQhua1geW8j+eKtuaAgeeahOWFieagh+aYvuekulxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldFNob3dDdXJzb3IoaXNTdHJlYW1pbmcpO1xuICB9LCBbaXNTdHJlYW1pbmddKTtcblxuICAvLyDnp7vpmaToh6rliqjmu5rliqjlip/og73vvIzpgb/lhY3lubLmibBcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgc3RyZWFtaW5nLW1hcmtkb3duLWNvbnRhaW5lciAke2NsYXNzTmFtZX1gfT5cbiAgICAgIHsvKiDkuLvopoHlhoXlrrkgKi99XG4gICAgICA8TWFya2Rvd25SZW5kZXJlciBjb250ZW50PXtkaXNwbGF5Q29udGVudH0gLz5cblxuICAgICAgey8qIOeugOWNleeahOa1geW8j+WFieaghyAtIOWPquWcqOecn+ato+a1geW8j+aXtuaYvuekuiAqL31cbiAgICAgIHtzaG93Q3Vyc29yICYmIGlzU3RyZWFtaW5nICYmIChcbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIHctMC41IGgtNCBiZy1ibHVlLTUwMCBtbC0xIGFuaW1hdGUtcHVsc2VcIiAvPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlUmVmIiwiTWFya2Rvd25SZW5kZXJlciIsIlN0cmVhbWluZ01hcmtkb3duUmVuZGVyZXIiLCJjb250ZW50IiwiaXNTdHJlYW1pbmciLCJjbGFzc05hbWUiLCJkaXNwbGF5Q29udGVudCIsInNldERpc3BsYXlDb250ZW50Iiwic2hvd0N1cnNvciIsInNldFNob3dDdXJzb3IiLCJjb250ZW50UmVmIiwibGFzdENvbnRlbnRMZW5ndGgiLCJjdXJyZW50IiwibGVuZ3RoIiwiZGl2Iiwic3BhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/StreamingMarkdownRenderer.tsx\n"));

/***/ })

});