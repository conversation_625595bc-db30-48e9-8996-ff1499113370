"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/StreamingMarkdownRenderer.tsx":
/*!***************************************************************!*\
  !*** ./src/app/chat/components/StreamingMarkdownRenderer.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMarkdownRenderer: () => (/* binding */ StreamingMarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MarkdownRenderer */ \"(app-pages-browser)/./src/app/chat/components/MarkdownRenderer.tsx\");\n/* __next_internal_client_entry_do_not_use__ StreamingMarkdownRenderer auto */ \nvar _s = $RefreshSig$();\n\n\nfunction StreamingMarkdownRenderer(param) {\n    let { content, isStreaming = false, className = '' } = param;\n    _s();\n    const [displayContent, setDisplayContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showCursor, setShowCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const lastContentLength = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    // 处理内容更新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n            setDisplayContent(content);\n            lastContentLength.current = content.length;\n        }\n    }[\"StreamingMarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    // 处理流式状态的光标显示\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n            setShowCursor(isStreaming);\n        }\n    }[\"StreamingMarkdownRenderer.useEffect\"], [\n        isStreaming\n    ]);\n    // 移除自动滚动功能，避免干扰\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"streaming-markdown-container \".concat(className),\n        style: {\n            display: 'flex',\n            flexDirection: 'column'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    flex: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_2__.MarkdownRenderer, {\n                    content: displayContent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            showCursor && isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"inline-block w-0.5 h-4 bg-blue-500 ml-1 animate-pulse\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(StreamingMarkdownRenderer, \"i0GzM5ClE6+Hb/6QcB70c6eSJeg=\");\n_c = StreamingMarkdownRenderer;\nvar _c;\n$RefreshReg$(_c, \"StreamingMarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/StreamingMarkdownRenderer.tsx\n"));

/***/ })

});