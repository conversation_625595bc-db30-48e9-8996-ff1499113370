"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/StreamingMarkdownRenderer.tsx":
/*!***************************************************************!*\
  !*** ./src/app/chat/components/StreamingMarkdownRenderer.tsx ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamingMarkdownRenderer: () => (/* binding */ StreamingMarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _MarkdownRenderer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MarkdownRenderer */ \"(app-pages-browser)/./src/app/chat/components/MarkdownRenderer.tsx\");\n/* __next_internal_client_entry_do_not_use__ StreamingMarkdownRenderer auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction StreamingMarkdownRenderer(param) {\n    let { content, isStreaming = false, className = '' } = param;\n    _s();\n    const [displayContent, setDisplayContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [showCursor, setShowCursor] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const lastContentLength = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\n    // 处理内容更新和光标显示\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n            if (content !== displayContent) {\n                setDisplayContent(content);\n                // 如果内容增加了，显示光标\n                if (content.length > lastContentLength.current) {\n                    setShowCursor(true);\n                    lastContentLength.current = content.length;\n                    // 短暂显示光标后隐藏\n                    const timer = setTimeout({\n                        \"StreamingMarkdownRenderer.useEffect.timer\": ()=>{\n                            setShowCursor(false);\n                        }\n                    }[\"StreamingMarkdownRenderer.useEffect.timer\"], 500);\n                    return ({\n                        \"StreamingMarkdownRenderer.useEffect\": ()=>clearTimeout(timer)\n                    })[\"StreamingMarkdownRenderer.useEffect\"];\n                }\n            }\n        }\n    }[\"StreamingMarkdownRenderer.useEffect\"], [\n        content,\n        displayContent\n    ]);\n    // 处理流式状态的光标闪烁\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n            if (isStreaming) {\n                setShowCursor(true);\n                const interval = setInterval({\n                    \"StreamingMarkdownRenderer.useEffect.interval\": ()=>{\n                        setShowCursor({\n                            \"StreamingMarkdownRenderer.useEffect.interval\": (prev)=>!prev\n                        }[\"StreamingMarkdownRenderer.useEffect.interval\"]);\n                    }\n                }[\"StreamingMarkdownRenderer.useEffect.interval\"], 600);\n                return ({\n                    \"StreamingMarkdownRenderer.useEffect\": ()=>clearInterval(interval)\n                })[\"StreamingMarkdownRenderer.useEffect\"];\n            } else {\n                setShowCursor(false);\n            }\n        }\n    }[\"StreamingMarkdownRenderer.useEffect\"], [\n        isStreaming\n    ]);\n    // 自动滚动到新内容\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n            if (contentRef.current && content.length > lastContentLength.current) {\n                const element = contentRef.current;\n                const isNearBottom = element.scrollTop + element.clientHeight >= element.scrollHeight - 100;\n                if (isNearBottom) {\n                    setTimeout({\n                        \"StreamingMarkdownRenderer.useEffect\": ()=>{\n                            element.scrollTop = element.scrollHeight;\n                        }\n                    }[\"StreamingMarkdownRenderer.useEffect\"], 50);\n                }\n            }\n        }\n    }[\"StreamingMarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: contentRef,\n        className: \"jsx-28be04fc9c921754\" + \" \" + \"streaming-markdown-container relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-28be04fc9c921754\" + \" \" + \"flex-1 min-w-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_3__.MarkdownRenderer, {\n                    content: displayContent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            showCursor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    animation: isStreaming ? 'pulse 1.2s infinite' : 'none'\n                },\n                className: \"jsx-28be04fc9c921754\" + \" \" + \"inline-block w-2 h-5 bg-blue-500 ml-1 transition-opacity duration-150 \".concat(showCursor ? 'opacity-100' : 'opacity-0')\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"28be04fc9c921754\",\n                children: \"@-webkit-keyframes pulse{0%,50%{opacity:1}51%,100%{opacity:.3}}@-moz-keyframes pulse{0%,50%{opacity:1}51%,100%{opacity:.3}}@-o-keyframes pulse{0%,50%{opacity:1}51%,100%{opacity:.3}}@keyframes pulse{0%,50%{opacity:1}51%,100%{opacity:.3}}.streaming-markdown-container.jsx-28be04fc9c921754{overflow-anchor:auto}.streaming-markdown-container.jsx-28be04fc9c921754 *{-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden;-webkit-font-smoothing:antialiased}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\StreamingMarkdownRenderer.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(StreamingMarkdownRenderer, \"BHP1QmQcn10gh5OvM1ODSa38FgA=\");\n_c = StreamingMarkdownRenderer;\nvar _c;\n$RefreshReg$(_c, \"StreamingMarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/StreamingMarkdownRenderer.tsx\n"));

/***/ })

});