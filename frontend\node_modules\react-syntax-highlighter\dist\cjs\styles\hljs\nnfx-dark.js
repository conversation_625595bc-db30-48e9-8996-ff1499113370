"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _default = exports["default"] = {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "background": "#333",
    "color": "#fff"
  },
  "xml .hljs-meta": {
    "fontWeight": "bold",
    "fontStyle": "italic",
    "color": "#69f"
  },
  "hljs-comment": {
    "fontStyle": "italic",
    "color": "#9c6"
  },
  "hljs-quote": {
    "fontStyle": "italic",
    "color": "#9c6"
  },
  "hljs-name": {
    "color": "#a7a",
    "fontWeight": "bold"
  },
  "hljs-keyword": {
    "color": "#a7a"
  },
  "hljs-attr": {
    "fontWeight": "bold",
    "color": "#fff"
  },
  "hljs-string": {
    "fontWeight": "normal",
    "color": "#bce"
  },
  "hljs-variable": {
    "color": "#588"
  },
  "hljs-template-variable": {
    "color": "#588"
  },
  "hljs-code": {
    "color": "#bce"
  },
  "hljs-meta-string": {
    "color": "#bce"
  },
  "hljs-number": {
    "color": "#bce"
  },
  "hljs-regexp": {
    "color": "#bce"
  },
  "hljs-link": {
    "color": "#bce"
  },
  "hljs-title": {
    "color": "#d40"
  },
  "hljs-symbol": {
    "color": "#d40"
  },
  "hljs-bullet": {
    "color": "#d40"
  },
  "hljs-built_in": {
    "color": "#d40"
  },
  "hljs-builtin-name": {
    "color": "#d40"
  },
  "hljs-section": {
    "color": "#a85"
  },
  "hljs-meta": {
    "color": "#a85"
  },
  "hljs-class .hljs-title": {
    "color": "#96c"
  },
  "hljs-type": {
    "color": "#96c"
  },
  "hljs-function .hljs-title": {
    "color": "#fff"
  },
  "hljs-subst": {
    "color": "#fff"
  },
  "hljs-formula": {
    "backgroundColor": "#eee",
    "fontStyle": "italic"
  },
  "hljs-addition": {
    "backgroundColor": "#797"
  },
  "hljs-deletion": {
    "backgroundColor": "#c99"
  },
  "hljs-selector-id": {
    "color": "#964"
  },
  "hljs-selector-class": {
    "color": "#964"
  },
  "hljs-doctag": {
    "fontWeight": "bold"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  }
};