"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _default = exports["default"] = {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "background": "#282b2e",
    "color": "#e0e2e4"
  },
  "hljs-keyword": {
    "color": "#93c763",
    "fontWeight": "bold"
  },
  "hljs-selector-tag": {
    "color": "#93c763",
    "fontWeight": "bold"
  },
  "hljs-literal": {
    "color": "#93c763",
    "fontWeight": "bold"
  },
  "hljs-selector-id": {
    "color": "#93c763"
  },
  "hljs-number": {
    "color": "#ffcd22"
  },
  "hljs-attribute": {
    "color": "#668bb0"
  },
  "hljs-code": {
    "color": "white"
  },
  "hljs-class .hljs-title": {
    "color": "white"
  },
  "hljs-section": {
    "color": "white",
    "fontWeight": "bold"
  },
  "hljs-regexp": {
    "color": "#d39745"
  },
  "hljs-link": {
    "color": "#d39745"
  },
  "hljs-meta": {
    "color": "#557182"
  },
  "hljs-tag": {
    "color": "#8cbbad"
  },
  "hljs-name": {
    "color": "#8cbbad",
    "fontWeight": "bold"
  },
  "hljs-bullet": {
    "color": "#8cbbad"
  },
  "hljs-subst": {
    "color": "#8cbbad"
  },
  "hljs-emphasis": {
    "color": "#8cbbad"
  },
  "hljs-type": {
    "color": "#8cbbad",
    "fontWeight": "bold"
  },
  "hljs-built_in": {
    "color": "#8cbbad"
  },
  "hljs-selector-attr": {
    "color": "#8cbbad"
  },
  "hljs-selector-pseudo": {
    "color": "#8cbbad"
  },
  "hljs-addition": {
    "color": "#8cbbad"
  },
  "hljs-variable": {
    "color": "#8cbbad"
  },
  "hljs-template-tag": {
    "color": "#8cbbad"
  },
  "hljs-template-variable": {
    "color": "#8cbbad"
  },
  "hljs-string": {
    "color": "#ec7600"
  },
  "hljs-symbol": {
    "color": "#ec7600"
  },
  "hljs-comment": {
    "color": "#818e96"
  },
  "hljs-quote": {
    "color": "#818e96"
  },
  "hljs-deletion": {
    "color": "#818e96"
  },
  "hljs-selector-class": {
    "color": "#A082BD"
  },
  "hljs-doctag": {
    "fontWeight": "bold"
  },
  "hljs-title": {
    "fontWeight": "bold"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};