'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { UseChatReturn, SendMessageContext, UseChatState } from './types';
import * as api from './api';
import { sendStreamMessage } from './streamHandlers';
import {
  saveChatState,
  getChatState,
  saveCurrentConversationId,
  saveSelectedModel,
  saveEnableTools,
  saveSelectedTools
} from '@/lib/storage';

export function useChat(): UseChatReturn {
  const router = useRouter();
  const searchParams = useSearchParams();

  // 从localStorage恢复状态
  const [isInitialized, setIsInitialized] = useState(false);
  const savedState = getChatState();

  // 状态管理
  const [models, setModels] = useState<UseChatState['models']>([]);
  const [conversations, setConversations] = useState<UseChatState['conversations']>([]);
  const [currentConversation, setCurrentConversation] = useState<UseChatState['currentConversation']>(null);
  const [messages, setMessages] = useState<UseChatState['messages']>([]);
  const [selectedModel, setSelectedModel] = useState<UseChatState['selectedModel']>(savedState.selectedModel || '');
  const [inputMessage, setInputMessage] = useState<UseChatState['inputMessage']>('');
  const [isLoading, setIsLoading] = useState<UseChatState['isLoading']>(false);
  const [isStreaming, setIsStreaming] = useState<UseChatState['isStreaming']>(false);
  const [error, setError] = useState<UseChatState['error']>(null);
  const [enableTools, setEnableTools] = useState<UseChatState['enableTools']>(savedState.enableTools || false);
  const [selectedTools, setSelectedTools] = useState<UseChatState['selectedTools']>(savedState.selectedTools || []);
  const [aiState, setAiState] = useState<UseChatState['aiState']>({ status: 'idle' });
  const [thinkingStartTime, setThinkingStartTime] = useState<UseChatState['thinkingStartTime']>(null);
  const [abortController, setAbortController] = useState<UseChatState['abortController']>(null);
  const [activeToolCalls, setActiveToolCalls] = useState<UseChatState['activeToolCalls']>(new Map());
  const [toolCallMessages, setToolCallMessages] = useState<UseChatState['toolCallMessages']>([]);

  // 加载模型列表
  const loadModels = async () => {
    const result = await api.loadModels();
    if (result.error) {
      setError(result.error);
    } else {
      setModels(result.models);
      // 如果没有选中的模型，使用第一个可用模型
      if (result.models.length > 0 && !selectedModel) {
        const defaultModel = result.models[0].name;
        setSelectedModel(defaultModel);
        saveSelectedModel(defaultModel);
      }
    }
  };

  // 加载对话列表
  const loadConversations = async () => {
    const result = await api.loadConversations();
    if (result.error) {
      console.error('加载对话列表失败:', result.error);
    } else {
      setConversations(result.conversations);
    }
  };

  // 加载特定对话
  const loadConversation = async (conversationId: number) => {
    const result = await api.loadConversation(conversationId);
    if (result.error) {
      setError(result.error);
    } else {
      setCurrentConversation(result.conversation!);
      setMessages(result.messages || []);
      setToolCallMessages(result.toolCallMessages || []);
      setActiveToolCalls(new Map());
      setSelectedModel(result.lastModel || result.conversation!.model);

      // 保存当前对话ID到localStorage
      saveCurrentConversationId(conversationId);

      if (searchParams.get('id') !== String(conversationId)) {
        router.push(`/chat?id=${conversationId}`);
      }
    }
  };

  // 创建新对话
  const createNewConversation = async () => {
    const result = await api.createNewConversation(selectedModel);
    if (result.error) {
      setError(result.error);
    } else {
      const newConversation = result.conversation!;
      setCurrentConversation(newConversation);
      setMessages([]);
      setToolCallMessages([]);
      setActiveToolCalls(new Map());

      // 保存新对话ID到localStorage
      saveCurrentConversationId(newConversation.id);

      await loadConversations();
      router.push(`/chat?id=${newConversation.id}`);
    }
  };

  // 删除对话
  const deleteConversation = async (conversationId: number) => {
    const result = await api.deleteConversation(conversationId);
    if (result.success) {
      if (currentConversation?.id === conversationId) {
        setCurrentConversation(null);
        setMessages([]);
        // 清除localStorage中的当前对话ID
        saveCurrentConversationId(null);
        router.push('/chat');
      }
      await loadConversations();
    } else if (result.error) {
      setError(result.error);
    }
  };

  // 发送消息
  const sendMessage = async () => {
    const context: SendMessageContext = {
      currentConversation,
      selectedModel,
      messages,
      inputMessage,
      enableTools,
      selectedTools,
    };

    await sendStreamMessage({
      context,
      setMessages,
      setIsStreaming,
      setAiState,
      setThinkingStartTime,
      setActiveToolCalls,
      setToolCallMessages,
      setInputMessage,
      setError,
      setAbortController,
      loadConversations,
      createNewConversation,
      messages,
      isStreaming,
    });
  };

  // 停止生成
  const stopGeneration = () => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
      setIsStreaming(false);
      setAiState({ status: 'idle' });
      setActiveToolCalls(new Map());
    }
  };

  // 清空当前对话
  const clearCurrentChat = async () => {
    if (!currentConversation) {
      return;
    }

    const result = await api.clearCurrentChat(currentConversation.id);
    if (result.success) {
      setMessages([]);
      setToolCallMessages([]); // 清空工具调用消息
      setActiveToolCalls(new Map()); // 清空活跃工具调用
      setError(null);
    } else if (result.error) {
      setError(result.error);
    }
  };

  // 处理模型切换
  const handleModelChange = (model: string) => {
    setSelectedModel(model);
    setAiState({ status: 'idle' });
    // 保存到localStorage
    saveSelectedModel(model);
  };

  // 处理工具开关
  const handleToolsToggle = (enabled: boolean) => {
    setEnableTools(enabled);
    // 保存到localStorage
    saveEnableTools(enabled);
  };

  // 处理选中工具变更
  const handleSelectedToolsChange = (tools: string[]) => {
    setSelectedTools(tools);
    // 保存到localStorage
    saveSelectedTools(tools);
  };

  // 初始化
  useEffect(() => {
    const initialize = async () => {
      await loadModels();
      await loadConversations();

      // 优先使用URL参数中的对话ID，其次使用localStorage中保存的ID
      const urlConversationId = searchParams.get('id');
      const savedConversationId = savedState.currentConversationId;

      let targetConversationId: number | null = null;

      if (urlConversationId) {
        targetConversationId = Number(urlConversationId);
      } else if (savedConversationId && !isInitialized) {
        // 只在首次初始化时使用保存的对话ID，避免重复跳转
        targetConversationId = savedConversationId;
        // 验证保存的对话ID是否仍然有效
        try {
          const result = await api.loadConversation(savedConversationId);
          if (result.conversation) {
            // 对话仍然存在，更新URL以反映恢复的对话
            router.replace(`/chat?id=${savedConversationId}`);
          } else {
            // 对话不存在，清除保存的ID
            saveCurrentConversationId(null);
            targetConversationId = null;
          }
        } catch (error) {
          console.warn('Failed to verify saved conversation:', error);
          saveCurrentConversationId(null);
          targetConversationId = null;
        }
      }

      if (targetConversationId) {
        // 避免在已有对话时重复加载
        if (currentConversation?.id !== targetConversationId) {
          await loadConversation(targetConversationId);
        }
      }

      setIsInitialized(true);
    };
    initialize();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]);

  // 验证并更新选中的模型
  useEffect(() => {
    if (models.length > 0 && selectedModel) {
      // 检查当前选中的模型是否仍然可用
      const modelExists = models.some(model => model.name === selectedModel);
      if (!modelExists) {
        // 如果模型不存在，选择第一个可用模型
        const defaultModel = models[0].name;
        setSelectedModel(defaultModel);
        saveSelectedModel(defaultModel);
      }
    }
  }, [models, selectedModel]);

  return {
    // 状态
    models,
    conversations,
    currentConversation,
    messages,
    selectedModel,
    inputMessage,
    isLoading,
    isStreaming,
    error,
    enableTools,
    selectedTools,
    aiState,
    activeToolCalls,
    toolCallMessages,
    thinkingStartTime,
    abortController,
    
    // 设置函数
    setSelectedModel: handleModelChange,
    setInputMessage,
    setError,
    setEnableTools: handleToolsToggle,
    setSelectedTools: handleSelectedToolsChange,
    
    // 操作函数
    loadConversation,
    createNewConversation,
    deleteConversation,
    sendMessage,
    stopGeneration,
    onToolsToggle: handleToolsToggle,
    clearCurrentChat,
  };
}