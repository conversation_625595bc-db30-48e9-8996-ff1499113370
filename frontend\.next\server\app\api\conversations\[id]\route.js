/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/conversations/[id]/route";
exports.ids = ["app/api/conversations/[id]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconversations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fconversations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconversations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconversations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fconversations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconversations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_conversations_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/conversations/[id]/route.ts */ \"(rsc)/./src/app/api/conversations/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/conversations/[id]/route\",\n        pathname: \"/api/conversations/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/conversations/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\api\\\\conversations\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_conversations_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconversations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fconversations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconversations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/conversations/[id]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/conversations/[id]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\n// 获取单个对话及其消息\nasync function GET(request, { params }) {\n    try {\n        const { id } = await params;\n        const conversationId = parseInt(id);\n        if (isNaN(conversationId)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '无效的对话ID'\n            }, {\n                status: 400\n            });\n        }\n        // 获取对话信息\n        const conversation = _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.getConversationById(conversationId);\n        if (!conversation) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '对话不存在'\n            }, {\n                status: 404\n            });\n        }\n        // 获取对话的所有消息\n        const messages = _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.getMessagesByConversationId(conversationId);\n        // 获取对话的工具调用记录（从messages表）\n        const toolCallRecords = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.getToolCallsByConversationId(conversationId);\n        // 获取对话中最后使用的模型\n        const lastModel = await _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.getLastModelByConversationId(conversationId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            conversation,\n            messages,\n            toolCallRecords,\n            lastModel\n        });\n    } catch (error) {\n        console.error('获取对话失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '获取对话失败',\n            message: error instanceof Error ? error.message : '未知错误'\n        }, {\n            status: 500\n        });\n    }\n}\n// 更新对话标题\nasync function PATCH(request, { params }) {\n    try {\n        const { id } = await params;\n        const conversationId = parseInt(id);\n        if (isNaN(conversationId)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '无效的对话ID'\n            }, {\n                status: 400\n            });\n        }\n        const body = await request.json();\n        const { title } = body;\n        if (!title || !title.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '标题不能为空'\n            }, {\n                status: 400\n            });\n        }\n        // 检查对话是否存在\n        const conversation = _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.getConversationById(conversationId);\n        if (!conversation) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '对话不存在'\n            }, {\n                status: 404\n            });\n        }\n        // 更新对话标题\n        _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.updateConversationTitle(conversationId, title.trim());\n        // 获取更新后的对话\n        const updatedConversation = _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.getConversationById(conversationId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            conversation: updatedConversation\n        });\n    } catch (error) {\n        console.error('更新对话失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '更新对话失败',\n            message: error instanceof Error ? error.message : '未知错误'\n        }, {\n            status: 500\n        });\n    }\n}\n// 删除对话\nasync function DELETE(request, { params }) {\n    try {\n        const { id } = await params;\n        const conversationId = parseInt(id);\n        if (isNaN(conversationId)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '无效的对话ID'\n            }, {\n                status: 400\n            });\n        }\n        // 检查对话是否存在\n        const conversation = _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.getConversationById(conversationId);\n        if (!conversation) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '对话不存在'\n            }, {\n                status: 404\n            });\n        }\n        // 删除对话（会级联删除相关消息）\n        _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.deleteConversation(conversationId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: '对话已删除'\n        });\n    } catch (error) {\n        console.error('删除对话失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '删除对话失败',\n            message: error instanceof Error ? error.message : '未知错误'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/conversations/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.conversationOperations),\n/* harmony export */   conversationQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.conversationQueries),\n/* harmony export */   db: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   dbOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.dbOperations),\n/* harmony export */   \"default\": () => (/* reexport safe */ _database_connection__WEBPACK_IMPORTED_MODULE_1__.db),\n/* harmony export */   initializeDatabase: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase),\n/* harmony export */   mcpDbOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpDbOperations),\n/* harmony export */   mcpServerOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpServerQueries),\n/* harmony export */   mcpToolOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolQueries),\n/* harmony export */   messageOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.messageOperations),\n/* harmony export */   messageQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.messageQueries)\n/* harmony export */ });\n/* harmony import */ var _database_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/index */ \"(rsc)/./src/lib/database/index.ts\");\n/* harmony import */ var _database_connection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database/connection */ \"(rsc)/./src/lib/database/connection.ts\");\n// 重新导出模块化的数据库操作\n// 这个文件现在作为向后兼容的入口点，所有实际的实现都在 ./database/ 目录下\n\n// 重新导出数据库连接作为默认导出（保持向后兼容性）\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGdCQUFnQjtBQUNoQiw2Q0FBNkM7QUFDWjtBQUVqQywyQkFBMkI7QUFDMkIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXHNyY1xcbGliXFxkYXRhYmFzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyDph43mlrDlr7zlh7rmqKHlnZfljJbnmoTmlbDmja7lupPmk43kvZxcbi8vIOi/meS4quaWh+S7tueOsOWcqOS9nOS4uuWQkeWQjuWFvOWuueeahOWFpeWPo+eCue+8jOaJgOacieWunumZheeahOWunueOsOmDveWcqCAuL2RhdGFiYXNlLyDnm67lvZXkuItcbmV4cG9ydCAqIGZyb20gJy4vZGF0YWJhc2UvaW5kZXgnO1xuXG4vLyDph43mlrDlr7zlh7rmlbDmja7lupPov57mjqXkvZzkuLrpu5jorqTlr7zlh7rvvIjkv53mjIHlkJHlkI7lhbzlrrnmgKfvvIlcbmV4cG9ydCB7IGRiIGFzIGRlZmF1bHQgfSBmcm9tICcuL2RhdGFiYXNlL2Nvbm5lY3Rpb24nOyJdLCJuYW1lcyI6WyJkYiIsImRlZmF1bHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connection.ts":
/*!****************************************!*\
  !*** ./src/lib/database/connection.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(better_sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// 数据库连接配置\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'chat.db');\nconst db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default())(dbPath);\n// 数据库初始化SQL\nconst initializeDatabase = ()=>{\n    // 先执行基础表创建（不包括custom_models表和相关索引）\n    const baseInitSQL = `\n    CREATE TABLE IF NOT EXISTS conversations (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      model TEXT NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    );\n\n    CREATE TABLE IF NOT EXISTS messages (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      conversation_id INTEGER NOT NULL,\n      role TEXT NOT NULL,\n      content TEXT NOT NULL,\n      model TEXT,\n      sequence_number INTEGER DEFAULT 0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      timestamp INTEGER, -- 毫秒级时间戳，用于精确排序\n      -- 工具调用相关字段\n      tool_name TEXT, -- 工具名称\n      tool_args TEXT, -- 工具参数 (JSON)\n      tool_result TEXT, -- 工具结果 (JSON)\n      tool_status TEXT CHECK (tool_status IN ('executing', 'completed', 'error')), -- 工具状态\n      tool_execution_time INTEGER, -- 工具执行时间(毫秒)\n      tool_error TEXT, -- 工具错误信息\n      -- Ollama生成统计信息\n      total_duration INTEGER,\n      load_duration INTEGER,\n      prompt_eval_count INTEGER,\n      prompt_eval_duration INTEGER,\n      eval_count INTEGER,\n      eval_duration INTEGER,\n      FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE\n    );\n\n    -- 自定义模型配置表（包含完整的Ollama API字段）\n    CREATE TABLE IF NOT EXISTS custom_models (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      base_model TEXT NOT NULL UNIQUE, -- 完整的基础模型名称\n      display_name TEXT NOT NULL, -- 用户可自定义的显示名称\n      model_hash TEXT NOT NULL UNIQUE, -- 内部使用的哈希名称\n      family TEXT NOT NULL, -- 模型家族信息\n      description TEXT,\n      system_prompt TEXT,\n      parameters TEXT, -- JSON格式存储所有参数\n      template TEXT, -- 自定义模板\n      license TEXT,\n      tags TEXT, -- JSON数组格式存储标签\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      size BIGINT,\n      digest TEXT,\n      ollama_modified_at TEXT,\n      -- Ollama API详细信息字段\n      architecture TEXT, -- 模型架构（llama、gemma等）\n      parameter_count INTEGER, -- 参数数量\n      context_length INTEGER, -- 上下文长度\n      embedding_length INTEGER, -- 嵌入维度\n      quantization_level TEXT, -- 量化级别（Q8_0、Q4_0等）\n      format TEXT, -- 文件格式（gguf等）\n      capabilities TEXT -- 模型能力（JSON数组格式：completion、vision等）\n    );\n\n    -- MCP服务器统一配置表\n    CREATE TABLE IF NOT EXISTS mcp_servers (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL UNIQUE,\n      display_name TEXT NOT NULL,\n      description TEXT,\n      type TEXT NOT NULL CHECK (type IN ('stdio', 'sse', 'streamable-http')),\n      status TEXT NOT NULL DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error', 'connecting')),\n      enabled BOOLEAN NOT NULL DEFAULT 1,\n      \n      -- STDIO配置\n      command TEXT,\n      args TEXT, -- JSON数组格式\n      working_directory TEXT,\n      \n      -- SSE/HTTP配置\n      url TEXT,\n      base_url TEXT,\n      port INTEGER,\n      path TEXT DEFAULT '/',\n      protocol TEXT DEFAULT 'http' CHECK (protocol IN ('http', 'https')),\n      \n      -- 通用配置\n      headers TEXT, -- JSON对象格式\n      auth_type TEXT CHECK (auth_type IN ('none', 'bearer', 'basic', 'api_key')),\n      auth_config TEXT, -- JSON格式\n      timeout_ms INTEGER DEFAULT 30000,\n      retry_attempts INTEGER DEFAULT 3,\n      retry_delay_ms INTEGER DEFAULT 1000,\n      \n      -- 扩展配置\n      extra_config TEXT, -- JSON格式，存储其他特殊配置\n      \n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      last_connected_at DATETIME,\n      error_message TEXT\n    );\n\n    -- MCP工具表\n    CREATE TABLE IF NOT EXISTS mcp_tools (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      server_id INTEGER NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT,\n      input_schema TEXT, -- JSON格式存储工具的输入参数模式\n      is_available BOOLEAN DEFAULT 1,\n      enabled BOOLEAN DEFAULT 1, -- 工具是否启用（在对话页面可见）\n      last_used_at DATETIME,\n      usage_count INTEGER DEFAULT 0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (server_id) REFERENCES mcp_servers (id) ON DELETE CASCADE,\n      UNIQUE(server_id, name)\n    );\n\n    -- 基础表索引\n    CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);\n    CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at DESC);\n    \n    -- 自定义模型相关索引\n    CREATE INDEX IF NOT EXISTS idx_custom_models_base_model ON custom_models(base_model);\n    CREATE INDEX IF NOT EXISTS idx_custom_models_hash ON custom_models(model_hash);\n    CREATE INDEX IF NOT EXISTS idx_custom_models_family ON custom_models(family);\n    \n    -- MCP相关索引\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_type ON mcp_servers(type);\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_status ON mcp_servers(status);\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_enabled ON mcp_servers(enabled);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_server_id ON mcp_tools(server_id);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_name ON mcp_tools(name);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_available ON mcp_tools(is_available);\n    -- 工具调用相关索引已迁移到messages表\n    CREATE INDEX IF NOT EXISTS idx_messages_tool_name ON messages(tool_name);\n    CREATE INDEX IF NOT EXISTS idx_messages_tool_status ON messages(tool_status);\n    CREATE INDEX IF NOT EXISTS idx_messages_conv_tool ON messages(conversation_id, tool_name);\n  `;\n    db.exec(baseInitSQL);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (db);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connection.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/conversations.ts":
/*!*******************************************!*\
  !*** ./src/lib/database/conversations.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationOperations: () => (/* binding */ conversationOperations),\n/* harmony export */   conversationQueries: () => (/* binding */ conversationQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// 对话相关查询语句\nconst conversationQueries = {\n    // 创建新对话\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO conversations (title, model)\n    VALUES (?, ?)\n  `),\n    // 获取所有对话\n    getAll: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM conversations\n    ORDER BY updated_at DESC\n  `),\n    // 根据ID获取对话\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM conversations\n    WHERE id = ?\n  `),\n    // 更新对话标题\n    updateTitle: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE conversations\n    SET title = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新对话的最后更新时间\n    updateTimestamp: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE conversations\n    SET updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除对话\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM conversations\n    WHERE id = ?\n  `)\n};\n// 对话数据库操作函数\nconst conversationOperations = {\n    // 创建新对话\n    create (data) {\n        const result = conversationQueries.create.run(data.title, data.model);\n        return result.lastInsertRowid;\n    },\n    // 获取所有对话\n    getAll () {\n        return conversationQueries.getAll.all();\n    },\n    // 根据ID获取对话\n    getById (id) {\n        return conversationQueries.getById.get(id);\n    },\n    // 更新对话标题\n    updateTitle (id, title) {\n        conversationQueries.updateTitle.run(title, id);\n    },\n    // 更新对话时间戳\n    updateTimestamp (id) {\n        conversationQueries.updateTimestamp.run(id);\n    },\n    // 删除对话\n    delete (id) {\n        conversationQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/conversations.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/index.ts":
/*!***********************************!*\
  !*** ./src/lib/database/index.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationOperations: () => (/* reexport safe */ _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations),\n/* harmony export */   conversationQueries: () => (/* reexport safe */ _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationQueries),\n/* harmony export */   db: () => (/* reexport safe */ _connection__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   dbOperations: () => (/* binding */ dbOperations),\n/* harmony export */   \"default\": () => (/* reexport safe */ _connection__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   initializeDatabase: () => (/* reexport safe */ _connection__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase),\n/* harmony export */   mcpDbOperations: () => (/* binding */ mcpDbOperations),\n/* harmony export */   mcpServerOperations: () => (/* reexport safe */ _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* reexport safe */ _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerQueries),\n/* harmony export */   mcpToolOperations: () => (/* reexport safe */ _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* reexport safe */ _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolQueries),\n/* harmony export */   messageOperations: () => (/* reexport safe */ _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations),\n/* harmony export */   messageQueries: () => (/* reexport safe */ _messages__WEBPACK_IMPORTED_MODULE_3__.messageQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/lib/database/types.ts\");\n/* harmony import */ var _conversations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conversations */ \"(rsc)/./src/lib/database/conversations.ts\");\n/* harmony import */ var _messages__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./messages */ \"(rsc)/./src/lib/database/messages.ts\");\n/* harmony import */ var _mcp_servers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mcp-servers */ \"(rsc)/./src/lib/database/mcp-servers.ts\");\n/* harmony import */ var _mcp_tools__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mcp-tools */ \"(rsc)/./src/lib/database/mcp-tools.ts\");\n// 导出数据库连接\n\n// 导出所有类型定义\n\n// 导出各模块的操作函数\n\n\n\n\n// MCP工具调用功能已迁移到messages表\n\n\n\n\nconst dbOperations = {\n    // 对话相关操作\n    createConversation: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.create,\n    getAllConversations: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.getAll,\n    getConversationById: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.getById,\n    updateConversationTitle: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.updateTitle,\n    updateConversationTimestamp: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.updateTimestamp,\n    deleteConversation: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.delete,\n    // 消息相关操作\n    createMessage: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.create,\n    getMessagesByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.getByConversationId,\n    deleteMessagesByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.deleteByConversationId,\n    getLastModelByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.getLastModelByConversationId,\n    // MCP工具调用相关操作\n    getToolCallsByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.getToolCallsByConversationId\n};\n// 兼容原有的 mcpDbOperations 对象\nconst mcpDbOperations = {\n    // MCP服务器相关操作\n    createMcpServer: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.create,\n    getAllMcpServers: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getAll,\n    getMcpServerById: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getById,\n    getMcpServerByName: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getByName,\n    getEnabledMcpServers: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getEnabled,\n    updateMcpServerStatus: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.updateStatus,\n    deleteMcpServer: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.delete,\n    // MCP工具相关操作\n    createMcpTool: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.create,\n    getMcpToolsByServerId: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getByServerId,\n    getMcpToolById: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getById,\n    getMcpToolByServerIdAndName: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getByServerIdAndName,\n    getAvailableMcpTools: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getAvailable,\n    updateMcpToolUsage: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateUsage,\n    updateMcpToolAvailability: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateAvailability,\n    updateMcpToolEnabled: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateEnabled,\n    deleteMcpToolsByServerId: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.deleteByServerId,\n    deleteMcpTool: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.delete\n};\n// 默认导出数据库连接（保持兼容性）\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/mcp-servers.ts":
/*!*****************************************!*\
  !*** ./src/lib/database/mcp-servers.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mcpServerOperations: () => (/* binding */ mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* binding */ mcpServerQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// MCP服务器相关查询语句\nconst mcpServerQueries = {\n    // 创建MCP服务器\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO mcp_servers (\n      name, display_name, description, type, enabled,\n      command, args, working_directory,\n      url, base_url, port, path, protocol,\n      headers, auth_type, auth_config, timeout_ms, retry_attempts, retry_delay_ms,\n      extra_config\n    )\n    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n  `),\n    // 获取所有MCP服务器\n    getAll: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    ORDER BY created_at DESC\n  `),\n    // 根据ID获取MCP服务器\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE id = ?\n  `),\n    // 根据名称获取MCP服务器\n    getByName: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE name = ?\n  `),\n    // 获取启用的MCP服务器\n    getEnabled: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE enabled = 1\n    ORDER BY created_at DESC\n  `),\n    // 更新MCP服务器状态\n    updateStatus: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_servers\n    SET status = ?, error_message = ?, updated_at = CURRENT_TIMESTAMP,\n        last_connected_at = CASE WHEN ? = 'connected' THEN CURRENT_TIMESTAMP ELSE last_connected_at END\n    WHERE id = ?\n  `),\n    // 更新MCP服务器配置\n    update: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_servers\n    SET display_name = ?, description = ?, type = ?, enabled = ?,\n        command = ?, args = ?, working_directory = ?,\n        url = ?, base_url = ?, port = ?, path = ?, protocol = ?,\n        headers = ?, auth_type = ?, auth_config = ?, timeout_ms = ?, retry_attempts = ?, retry_delay_ms = ?,\n        extra_config = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除MCP服务器\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_servers\n    WHERE id = ?\n  `)\n};\n// MCP服务器数据库操作函数\nconst mcpServerOperations = {\n    // 创建MCP服务器\n    create (data) {\n        const result = mcpServerQueries.create.run(data.name, data.display_name, data.description || null, data.type, Boolean(data.enabled ?? true) ? 1 : 0, data.command || null, data.args ? JSON.stringify(data.args) : null, data.working_directory || null, data.url || null, data.base_url || null, data.port ? Number(data.port) : null, data.path || null, data.protocol || null, data.headers ? JSON.stringify(data.headers) : null, data.auth_type || null, data.auth_config ? JSON.stringify(data.auth_config) : null, data.timeout_ms ? Number(data.timeout_ms) : null, data.retry_attempts ? Number(data.retry_attempts) : null, data.retry_delay_ms ? Number(data.retry_delay_ms) : null, data.extra_config ? JSON.stringify(data.extra_config) : null);\n        return result.lastInsertRowid;\n    },\n    // 获取所有MCP服务器\n    getAll () {\n        return mcpServerQueries.getAll.all();\n    },\n    // 根据ID获取MCP服务器\n    getById (id) {\n        return mcpServerQueries.getById.get(id);\n    },\n    // 根据名称获取MCP服务器\n    getByName (name) {\n        return mcpServerQueries.getByName.get(name);\n    },\n    // 获取启用的MCP服务器\n    getEnabled () {\n        return mcpServerQueries.getEnabled.all();\n    },\n    // 更新MCP服务器状态\n    updateStatus (id, status, errorMessage) {\n        mcpServerQueries.updateStatus.run(status, errorMessage || null, status, id);\n    },\n    // 删除MCP服务器\n    delete (id) {\n        mcpServerQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/mcp-servers.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/mcp-tools.ts":
/*!***************************************!*\
  !*** ./src/lib/database/mcp-tools.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mcpToolOperations: () => (/* binding */ mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* binding */ mcpToolQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// MCP工具相关查询语句\nconst mcpToolQueries = {\n    // 创建MCP工具\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO mcp_tools (server_id, name, description, input_schema, is_available, enabled)\n    VALUES (?, ?, ?, ?, ?, ?)\n  `),\n    // 获取服务器的所有工具\n    getByServerId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE server_id = ?\n    ORDER BY name ASC\n  `),\n    // 根据ID获取工具\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE id = ?\n  `),\n    // 根据服务器ID和工具名称获取工具\n    getByServerIdAndName: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE server_id = ? AND name = ?\n  `),\n    // 获取可用的工具\n    getAvailable: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT t.*, s.name as server_name, s.status as server_status\n    FROM mcp_tools t\n    JOIN mcp_servers s ON t.server_id = s.id\n    WHERE t.is_available = 1 AND t.enabled = 1 AND s.enabled = 1 AND s.status = 'connected'\n    ORDER BY t.name ASC\n  `),\n    // 更新工具使用统计\n    updateUsage: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET usage_count = usage_count + 1, last_used_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新工具可用性\n    updateAvailability: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET is_available = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新工具启用状态\n    updateEnabled: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET enabled = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除服务器的所有工具\n    deleteByServerId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tools\n    WHERE server_id = ?\n  `),\n    // 删除工具\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tools\n    WHERE id = ?\n  `)\n};\n// MCP工具数据库操作函数\nconst mcpToolOperations = {\n    // 创建MCP工具\n    create (data) {\n        const result = mcpToolQueries.create.run(data.server_id, data.name, data.description || null, data.input_schema ? JSON.stringify(data.input_schema) : null, Boolean(data.is_available ?? true) ? 1 : 0, Boolean(data.enabled ?? true) ? 1 : 0 // 确保布尔值转换为数字\n        );\n        return result.lastInsertRowid;\n    },\n    // 获取服务器的所有工具\n    getByServerId (serverId) {\n        return mcpToolQueries.getByServerId.all(serverId);\n    },\n    // 根据ID获取工具\n    getById (id) {\n        return mcpToolQueries.getById.get(id);\n    },\n    // 根据服务器ID和工具名称获取工具\n    getByServerIdAndName (serverId, name) {\n        return mcpToolQueries.getByServerIdAndName.get(serverId, name);\n    },\n    // 获取可用的工具\n    getAvailable () {\n        return mcpToolQueries.getAvailable.all();\n    },\n    // 更新工具使用统计\n    updateUsage (toolId) {\n        mcpToolQueries.updateUsage.run(toolId);\n    },\n    // 更新工具可用性\n    updateAvailability (id, isAvailable) {\n        mcpToolQueries.updateAvailability.run(isAvailable ? 1 : 0, id);\n    },\n    // 更新工具启用状态\n    updateEnabled (id, enabled) {\n        mcpToolQueries.updateEnabled.run(enabled ? 1 : 0, id);\n    },\n    // 删除服务器的所有工具\n    deleteByServerId (serverId) {\n        mcpToolQueries.deleteByServerId.run(serverId);\n    },\n    // 删除工具\n    delete (id) {\n        mcpToolQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGFiYXNlL21jcC10b29scy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBa0M7QUFHbEMsY0FBYztBQUNQLE1BQU1DLGlCQUFpQjtJQUM1QixVQUFVO0lBQ1ZDLFFBQVFGLDJDQUFFQSxDQUFDRyxPQUFPLENBQUMsQ0FBQzs7O0VBR3BCLENBQUM7SUFFRCxhQUFhO0lBQ2JDLGVBQWVKLDJDQUFFQSxDQUFDRyxPQUFPLENBQUMsQ0FBQzs7OztFQUkzQixDQUFDO0lBRUQsV0FBVztJQUNYRSxTQUFTTCwyQ0FBRUEsQ0FBQ0csT0FBTyxDQUFDLENBQUM7OztFQUdyQixDQUFDO0lBRUQsbUJBQW1CO0lBQ25CRyxzQkFBc0JOLDJDQUFFQSxDQUFDRyxPQUFPLENBQUMsQ0FBQzs7O0VBR2xDLENBQUM7SUFFRCxVQUFVO0lBQ1ZJLGNBQWNQLDJDQUFFQSxDQUFDRyxPQUFPLENBQUMsQ0FBQzs7Ozs7O0VBTTFCLENBQUM7SUFFRCxXQUFXO0lBQ1hLLGFBQWFSLDJDQUFFQSxDQUFDRyxPQUFPLENBQUMsQ0FBQzs7OztFQUl6QixDQUFDO0lBRUQsVUFBVTtJQUNWTSxvQkFBb0JULDJDQUFFQSxDQUFDRyxPQUFPLENBQUMsQ0FBQzs7OztFQUloQyxDQUFDO0lBRUQsV0FBVztJQUNYTyxlQUFlViwyQ0FBRUEsQ0FBQ0csT0FBTyxDQUFDLENBQUM7Ozs7RUFJM0IsQ0FBQztJQUVELGFBQWE7SUFDYlEsa0JBQWtCWCwyQ0FBRUEsQ0FBQ0csT0FBTyxDQUFDLENBQUM7OztFQUc5QixDQUFDO0lBRUQsT0FBTztJQUNQUyxRQUFRWiwyQ0FBRUEsQ0FBQ0csT0FBTyxDQUFDLENBQUM7OztFQUdwQixDQUFDO0FBQ0gsRUFBRTtBQUVGLGVBQWU7QUFDUixNQUFNVSxvQkFBb0I7SUFDL0IsVUFBVTtJQUNWWCxRQUFPWSxJQUF1QjtRQUM1QixNQUFNQyxTQUFTZCxlQUFlQyxNQUFNLENBQUNjLEdBQUcsQ0FDdENGLEtBQUtHLFNBQVMsRUFDZEgsS0FBS0ksSUFBSSxFQUNUSixLQUFLSyxXQUFXLElBQUksTUFDcEJMLEtBQUtNLFlBQVksR0FBR0MsS0FBS0MsU0FBUyxDQUFDUixLQUFLTSxZQUFZLElBQUksTUFDeERHLFFBQVFULEtBQUtVLFlBQVksSUFBSSxRQUFRLElBQUksR0FDekNELFFBQVFULEtBQUtXLE9BQU8sSUFBSSxRQUFRLElBQUksRUFBRSxhQUFhOztRQUVyRCxPQUFPVixPQUFPVyxlQUFlO0lBQy9CO0lBRUEsYUFBYTtJQUNidEIsZUFBY3VCLFFBQWdCO1FBQzVCLE9BQU8xQixlQUFlRyxhQUFhLENBQUN3QixHQUFHLENBQUNEO0lBQzFDO0lBRUEsV0FBVztJQUNYdEIsU0FBUXdCLEVBQVU7UUFDaEIsT0FBTzVCLGVBQWVJLE9BQU8sQ0FBQ3lCLEdBQUcsQ0FBQ0Q7SUFDcEM7SUFFQSxtQkFBbUI7SUFDbkJ2QixzQkFBcUJxQixRQUFnQixFQUFFVCxJQUFZO1FBQ2pELE9BQU9qQixlQUFlSyxvQkFBb0IsQ0FBQ3dCLEdBQUcsQ0FBQ0gsVUFBVVQ7SUFDM0Q7SUFFQSxVQUFVO0lBQ1ZYO1FBQ0UsT0FBT04sZUFBZU0sWUFBWSxDQUFDcUIsR0FBRztJQUN4QztJQUVBLFdBQVc7SUFDWHBCLGFBQVl1QixNQUFjO1FBQ3hCOUIsZUFBZU8sV0FBVyxDQUFDUSxHQUFHLENBQUNlO0lBQ2pDO0lBRUEsVUFBVTtJQUNWdEIsb0JBQW1Cb0IsRUFBVSxFQUFFRyxXQUFvQjtRQUNqRC9CLGVBQWVRLGtCQUFrQixDQUFDTyxHQUFHLENBQUNnQixjQUFjLElBQUksR0FBR0g7SUFDN0Q7SUFFQSxXQUFXO0lBQ1huQixlQUFjbUIsRUFBVSxFQUFFSixPQUFnQjtRQUN4Q3hCLGVBQWVTLGFBQWEsQ0FBQ00sR0FBRyxDQUFDUyxVQUFVLElBQUksR0FBR0k7SUFDcEQ7SUFFQSxhQUFhO0lBQ2JsQixrQkFBaUJnQixRQUFnQjtRQUMvQjFCLGVBQWVVLGdCQUFnQixDQUFDSyxHQUFHLENBQUNXO0lBQ3RDO0lBRUEsT0FBTztJQUNQZixRQUFPaUIsRUFBVTtRQUNmNUIsZUFBZVcsTUFBTSxDQUFDSSxHQUFHLENBQUNhO0lBQzVCO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcc3JjXFxsaWJcXGRhdGFiYXNlXFxtY3AtdG9vbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGIgfSBmcm9tICcuL2Nvbm5lY3Rpb24nO1xuaW1wb3J0IHR5cGUgeyBNY3BUb29sLCBDcmVhdGVNY3BUb29sRGF0YSB9IGZyb20gJy4vdHlwZXMnO1xuXG4vLyBNQ1Dlt6Xlhbfnm7jlhbPmn6Xor6Lor63lj6VcbmV4cG9ydCBjb25zdCBtY3BUb29sUXVlcmllcyA9IHtcbiAgLy8g5Yib5bu6TUNQ5bel5YW3XG4gIGNyZWF0ZTogZGIucHJlcGFyZShgXG4gICAgSU5TRVJUIElOVE8gbWNwX3Rvb2xzIChzZXJ2ZXJfaWQsIG5hbWUsIGRlc2NyaXB0aW9uLCBpbnB1dF9zY2hlbWEsIGlzX2F2YWlsYWJsZSwgZW5hYmxlZClcbiAgICBWQUxVRVMgKD8sID8sID8sID8sID8sID8pXG4gIGApLFxuXG4gIC8vIOiOt+WPluacjeWKoeWZqOeahOaJgOacieW3peWFt1xuICBnZXRCeVNlcnZlcklkOiBkYi5wcmVwYXJlKGBcbiAgICBTRUxFQ1QgKiBGUk9NIG1jcF90b29sc1xuICAgIFdIRVJFIHNlcnZlcl9pZCA9ID9cbiAgICBPUkRFUiBCWSBuYW1lIEFTQ1xuICBgKSxcblxuICAvLyDmoLnmja5JROiOt+WPluW3peWFt1xuICBnZXRCeUlkOiBkYi5wcmVwYXJlKGBcbiAgICBTRUxFQ1QgKiBGUk9NIG1jcF90b29sc1xuICAgIFdIRVJFIGlkID0gP1xuICBgKSxcblxuICAvLyDmoLnmja7mnI3liqHlmahJROWSjOW3peWFt+WQjeensOiOt+WPluW3peWFt1xuICBnZXRCeVNlcnZlcklkQW5kTmFtZTogZGIucHJlcGFyZShgXG4gICAgU0VMRUNUICogRlJPTSBtY3BfdG9vbHNcbiAgICBXSEVSRSBzZXJ2ZXJfaWQgPSA/IEFORCBuYW1lID0gP1xuICBgKSxcblxuICAvLyDojrflj5blj6/nlKjnmoTlt6XlhbdcbiAgZ2V0QXZhaWxhYmxlOiBkYi5wcmVwYXJlKGBcbiAgICBTRUxFQ1QgdC4qLCBzLm5hbWUgYXMgc2VydmVyX25hbWUsIHMuc3RhdHVzIGFzIHNlcnZlcl9zdGF0dXNcbiAgICBGUk9NIG1jcF90b29scyB0XG4gICAgSk9JTiBtY3Bfc2VydmVycyBzIE9OIHQuc2VydmVyX2lkID0gcy5pZFxuICAgIFdIRVJFIHQuaXNfYXZhaWxhYmxlID0gMSBBTkQgdC5lbmFibGVkID0gMSBBTkQgcy5lbmFibGVkID0gMSBBTkQgcy5zdGF0dXMgPSAnY29ubmVjdGVkJ1xuICAgIE9SREVSIEJZIHQubmFtZSBBU0NcbiAgYCksXG5cbiAgLy8g5pu05paw5bel5YW35L2/55So57uf6K6hXG4gIHVwZGF0ZVVzYWdlOiBkYi5wcmVwYXJlKGBcbiAgICBVUERBVEUgbWNwX3Rvb2xzXG4gICAgU0VUIHVzYWdlX2NvdW50ID0gdXNhZ2VfY291bnQgKyAxLCBsYXN0X3VzZWRfYXQgPSBDVVJSRU5UX1RJTUVTVEFNUCwgdXBkYXRlZF9hdCA9IENVUlJFTlRfVElNRVNUQU1QXG4gICAgV0hFUkUgaWQgPSA/XG4gIGApLFxuXG4gIC8vIOabtOaWsOW3peWFt+WPr+eUqOaAp1xuICB1cGRhdGVBdmFpbGFiaWxpdHk6IGRiLnByZXBhcmUoYFxuICAgIFVQREFURSBtY3BfdG9vbHNcbiAgICBTRVQgaXNfYXZhaWxhYmxlID0gPywgdXBkYXRlZF9hdCA9IENVUlJFTlRfVElNRVNUQU1QXG4gICAgV0hFUkUgaWQgPSA/XG4gIGApLFxuXG4gIC8vIOabtOaWsOW3peWFt+WQr+eUqOeKtuaAgVxuICB1cGRhdGVFbmFibGVkOiBkYi5wcmVwYXJlKGBcbiAgICBVUERBVEUgbWNwX3Rvb2xzXG4gICAgU0VUIGVuYWJsZWQgPSA/LCB1cGRhdGVkX2F0ID0gQ1VSUkVOVF9USU1FU1RBTVBcbiAgICBXSEVSRSBpZCA9ID9cbiAgYCksXG5cbiAgLy8g5Yig6Zmk5pyN5Yqh5Zmo55qE5omA5pyJ5bel5YW3XG4gIGRlbGV0ZUJ5U2VydmVySWQ6IGRiLnByZXBhcmUoYFxuICAgIERFTEVURSBGUk9NIG1jcF90b29sc1xuICAgIFdIRVJFIHNlcnZlcl9pZCA9ID9cbiAgYCksXG5cbiAgLy8g5Yig6Zmk5bel5YW3XG4gIGRlbGV0ZTogZGIucHJlcGFyZShgXG4gICAgREVMRVRFIEZST00gbWNwX3Rvb2xzXG4gICAgV0hFUkUgaWQgPSA/XG4gIGApLFxufTtcblxuLy8gTUNQ5bel5YW35pWw5o2u5bqT5pON5L2c5Ye95pWwXG5leHBvcnQgY29uc3QgbWNwVG9vbE9wZXJhdGlvbnMgPSB7XG4gIC8vIOWIm+W7uk1DUOW3peWFt1xuICBjcmVhdGUoZGF0YTogQ3JlYXRlTWNwVG9vbERhdGEpOiBudW1iZXIge1xuICAgIGNvbnN0IHJlc3VsdCA9IG1jcFRvb2xRdWVyaWVzLmNyZWF0ZS5ydW4oXG4gICAgICBkYXRhLnNlcnZlcl9pZCxcbiAgICAgIGRhdGEubmFtZSxcbiAgICAgIGRhdGEuZGVzY3JpcHRpb24gfHwgbnVsbCxcbiAgICAgIGRhdGEuaW5wdXRfc2NoZW1hID8gSlNPTi5zdHJpbmdpZnkoZGF0YS5pbnB1dF9zY2hlbWEpIDogbnVsbCxcbiAgICAgIEJvb2xlYW4oZGF0YS5pc19hdmFpbGFibGUgPz8gdHJ1ZSkgPyAxIDogMCwgLy8g56Gu5L+d5biD5bCU5YC86L2s5o2i5Li65pWw5a2XXG4gICAgICBCb29sZWFuKGRhdGEuZW5hYmxlZCA/PyB0cnVlKSA/IDEgOiAwIC8vIOehruS/neW4g+WwlOWAvOi9rOaNouS4uuaVsOWtl1xuICAgICk7XG4gICAgcmV0dXJuIHJlc3VsdC5sYXN0SW5zZXJ0Um93aWQgYXMgbnVtYmVyO1xuICB9LFxuXG4gIC8vIOiOt+WPluacjeWKoeWZqOeahOaJgOacieW3peWFt1xuICBnZXRCeVNlcnZlcklkKHNlcnZlcklkOiBudW1iZXIpOiBNY3BUb29sW10ge1xuICAgIHJldHVybiBtY3BUb29sUXVlcmllcy5nZXRCeVNlcnZlcklkLmFsbChzZXJ2ZXJJZCkgYXMgTWNwVG9vbFtdO1xuICB9LFxuXG4gIC8vIOagueaNrklE6I635Y+W5bel5YW3XG4gIGdldEJ5SWQoaWQ6IG51bWJlcik6IE1jcFRvb2wgfCB1bmRlZmluZWQge1xuICAgIHJldHVybiBtY3BUb29sUXVlcmllcy5nZXRCeUlkLmdldChpZCkgYXMgTWNwVG9vbCB8IHVuZGVmaW5lZDtcbiAgfSxcblxuICAvLyDmoLnmja7mnI3liqHlmahJROWSjOW3peWFt+WQjeensOiOt+WPluW3peWFt1xuICBnZXRCeVNlcnZlcklkQW5kTmFtZShzZXJ2ZXJJZDogbnVtYmVyLCBuYW1lOiBzdHJpbmcpOiBNY3BUb29sIHwgdW5kZWZpbmVkIHtcbiAgICByZXR1cm4gbWNwVG9vbFF1ZXJpZXMuZ2V0QnlTZXJ2ZXJJZEFuZE5hbWUuZ2V0KHNlcnZlcklkLCBuYW1lKSBhcyBNY3BUb29sIHwgdW5kZWZpbmVkO1xuICB9LFxuXG4gIC8vIOiOt+WPluWPr+eUqOeahOW3peWFt1xuICBnZXRBdmFpbGFibGUoKTogYW55W10ge1xuICAgIHJldHVybiBtY3BUb29sUXVlcmllcy5nZXRBdmFpbGFibGUuYWxsKCk7XG4gIH0sXG5cbiAgLy8g5pu05paw5bel5YW35L2/55So57uf6K6hXG4gIHVwZGF0ZVVzYWdlKHRvb2xJZDogbnVtYmVyKTogdm9pZCB7XG4gICAgbWNwVG9vbFF1ZXJpZXMudXBkYXRlVXNhZ2UucnVuKHRvb2xJZCk7XG4gIH0sXG5cbiAgLy8g5pu05paw5bel5YW35Y+v55So5oCnXG4gIHVwZGF0ZUF2YWlsYWJpbGl0eShpZDogbnVtYmVyLCBpc0F2YWlsYWJsZTogYm9vbGVhbik6IHZvaWQge1xuICAgIG1jcFRvb2xRdWVyaWVzLnVwZGF0ZUF2YWlsYWJpbGl0eS5ydW4oaXNBdmFpbGFibGUgPyAxIDogMCwgaWQpO1xuICB9LFxuXG4gIC8vIOabtOaWsOW3peWFt+WQr+eUqOeKtuaAgVxuICB1cGRhdGVFbmFibGVkKGlkOiBudW1iZXIsIGVuYWJsZWQ6IGJvb2xlYW4pOiB2b2lkIHtcbiAgICBtY3BUb29sUXVlcmllcy51cGRhdGVFbmFibGVkLnJ1bihlbmFibGVkID8gMSA6IDAsIGlkKTtcbiAgfSxcblxuICAvLyDliKDpmaTmnI3liqHlmajnmoTmiYDmnInlt6XlhbdcbiAgZGVsZXRlQnlTZXJ2ZXJJZChzZXJ2ZXJJZDogbnVtYmVyKTogdm9pZCB7XG4gICAgbWNwVG9vbFF1ZXJpZXMuZGVsZXRlQnlTZXJ2ZXJJZC5ydW4oc2VydmVySWQpO1xuICB9LFxuXG4gIC8vIOWIoOmZpOW3peWFt1xuICBkZWxldGUoaWQ6IG51bWJlcik6IHZvaWQge1xuICAgIG1jcFRvb2xRdWVyaWVzLmRlbGV0ZS5ydW4oaWQpO1xuICB9LFxufTsiXSwibmFtZXMiOlsiZGIiLCJtY3BUb29sUXVlcmllcyIsImNyZWF0ZSIsInByZXBhcmUiLCJnZXRCeVNlcnZlcklkIiwiZ2V0QnlJZCIsImdldEJ5U2VydmVySWRBbmROYW1lIiwiZ2V0QXZhaWxhYmxlIiwidXBkYXRlVXNhZ2UiLCJ1cGRhdGVBdmFpbGFiaWxpdHkiLCJ1cGRhdGVFbmFibGVkIiwiZGVsZXRlQnlTZXJ2ZXJJZCIsImRlbGV0ZSIsIm1jcFRvb2xPcGVyYXRpb25zIiwiZGF0YSIsInJlc3VsdCIsInJ1biIsInNlcnZlcl9pZCIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsImlucHV0X3NjaGVtYSIsIkpTT04iLCJzdHJpbmdpZnkiLCJCb29sZWFuIiwiaXNfYXZhaWxhYmxlIiwiZW5hYmxlZCIsImxhc3RJbnNlcnRSb3dpZCIsInNlcnZlcklkIiwiYWxsIiwiaWQiLCJnZXQiLCJ0b29sSWQiLCJpc0F2YWlsYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/mcp-tools.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/messages.ts":
/*!**************************************!*\
  !*** ./src/lib/database/messages.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   messageOperations: () => (/* binding */ messageOperations),\n/* harmony export */   messageQueries: () => (/* binding */ messageQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _conversations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./conversations */ \"(rsc)/./src/lib/database/conversations.ts\");\n\n\n// 消息相关查询语句\nconst messageQueries = {\n    // 创建新消息\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO messages (\n      conversation_id, role, content, model, sequence_number, timestamp,\n      total_duration, load_duration, prompt_eval_count, prompt_eval_duration,\n      eval_count, eval_duration\n    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n  `),\n    // 获取对话的所有消息\n    getByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM messages\n    WHERE conversation_id = ?\n    ORDER BY id ASC\n  `),\n    // 删除对话的所有消息\n    deleteByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM messages WHERE conversation_id = ?\n  `),\n    // 获取对话的工具调用记录（从messages表）\n    getToolCallsByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT \n      id,\n      conversation_id,\n      tool_name,\n      tool_args,\n      tool_result,\n      tool_status,\n      tool_execution_time,\n      tool_error,\n      created_at,\n      timestamp\n    FROM messages \n    WHERE conversation_id = ? AND tool_name IS NOT NULL\n    ORDER BY id ASC\n  `),\n    // 获取对话中最后使用的模型\n    getLastModelByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT model FROM messages\n    WHERE conversation_id = ? AND model IS NOT NULL\n    ORDER BY created_at DESC\n    LIMIT 1\n  `),\n    // 获取对话中下一个可用的序列号\n    getNextSequenceNumber: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT COALESCE(MAX(sequence_number), 0) + 1 as next_sequence\n    FROM messages\n    WHERE conversation_id = ?\n  `)\n};\n// 消息数据库操作函数\nconst messageOperations = {\n    // 创建新消息\n    create (data) {\n        // 简化：不再使用sequence_number，只依赖自增ID\n        // 生成时间戳（毫秒级）\n        const timestamp = Date.now();\n        const result = messageQueries.create.run(data.conversation_id, data.role, data.content, data.model || null, 0, timestamp, data.total_duration || null, data.load_duration || null, data.prompt_eval_count || null, data.prompt_eval_duration || null, data.eval_count || null, data.eval_duration || null);\n        // 更新对话的时间戳\n        _conversations__WEBPACK_IMPORTED_MODULE_1__.conversationOperations.updateTimestamp(data.conversation_id);\n        return result.lastInsertRowid;\n    },\n    // 获取对话的所有消息\n    getByConversationId (conversationId) {\n        return messageQueries.getByConversationId.all(conversationId);\n    },\n    // 删除对话的所有消息\n    deleteByConversationId (conversationId) {\n        messageQueries.deleteByConversationId.run(conversationId);\n    },\n    // 获取对话的工具调用记录\n    getToolCallsByConversationId (conversationId) {\n        return messageQueries.getToolCallsByConversationId.all(conversationId);\n    },\n    // 获取对话中最后使用的模型\n    getLastModelByConversationId (conversationId) {\n        const result = messageQueries.getLastModelByConversationId.get(conversationId);\n        return result?.model || null;\n    },\n    // 获取对话中下一个可用的序列号（已废弃，保留兼容性）\n    getNextSequenceNumber (conversationId) {\n        const result = messageQueries.getNextSequenceNumber.get(conversationId);\n        return result?.next_sequence || 1;\n    },\n    // 创建工具调用消息\n    createToolCall (data) {\n        const timestamp = Date.now();\n        const result = messageQueries.create.run(data.conversation_id, 'tool_call', `工具调用: ${data.tool_name}`, null, 0, timestamp, null, null, null, null, null, null // 统计信息字段\n        );\n        // 更新工具相关字段\n        if (result.lastInsertRowid) {\n            const updateToolFields = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n        UPDATE messages SET\n          tool_name = ?,\n          tool_args = ?,\n          tool_result = ?,\n          tool_status = ?,\n          tool_execution_time = ?,\n          tool_error = ?\n        WHERE id = ?\n      `);\n            updateToolFields.run(data.tool_name, JSON.stringify(data.tool_args), data.tool_result ? JSON.stringify(data.tool_result) : null, data.tool_status, data.tool_execution_time || null, data.tool_error || null, result.lastInsertRowid);\n        }\n        _conversations__WEBPACK_IMPORTED_MODULE_1__.conversationOperations.updateTimestamp(data.conversation_id);\n        return result.lastInsertRowid;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/messages.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/types.ts":
/*!***********************************!*\
  !*** ./src/lib/database/types.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// 对话相关接口\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGFiYXNlL3R5cGVzLnRzIiwibWFwcGluZ3MiOiI7QUFBQSxTQUFTO0FBZ0xSIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxzcmNcXGxpYlxcZGF0YWJhc2VcXHR5cGVzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIOWvueivneebuOWFs+aOpeWPo1xuZXhwb3J0IGludGVyZmFjZSBDb252ZXJzYXRpb24ge1xuICBpZDogbnVtYmVyO1xuICB0aXRsZTogc3RyaW5nO1xuICBtb2RlbDogc3RyaW5nO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIHVwZGF0ZWRfYXQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBNZXNzYWdlIHtcbiAgaWQ6IG51bWJlcjtcbiAgY29udmVyc2F0aW9uX2lkOiBudW1iZXI7XG4gIHJvbGU6ICd1c2VyJyB8ICdhc3Npc3RhbnQnIHwgJ3N5c3RlbScgfCAndG9vbCcgfCAndG9vbF9jYWxsJyB8ICd0b29sX3Jlc3VsdCc7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgbW9kZWw/OiBzdHJpbmc7XG4gIHNlcXVlbmNlX251bWJlcj86IG51bWJlcjtcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xuICB0aW1lc3RhbXA/OiBudW1iZXI7IC8vIOeUqOS6jua2iOaBr+aOkuW6j+eahOaXtumXtOaIs1xuICAvLyDlt6XlhbfosIPnlKjnm7jlhbPlrZfmrrVcbiAgdG9vbF9uYW1lPzogc3RyaW5nO1xuICB0b29sX2FyZ3M/OiBzdHJpbmc7IC8vIEpTT07lrZfnrKbkuLJcbiAgdG9vbF9yZXN1bHQ/OiBzdHJpbmc7IC8vIEpTT07lrZfnrKbkuLJcbiAgdG9vbF9zdGF0dXM/OiAnZXhlY3V0aW5nJyB8ICdjb21wbGV0ZWQnIHwgJ2Vycm9yJztcbiAgdG9vbF9leGVjdXRpb25fdGltZT86IG51bWJlcjsgLy8g5q+r56eSXG4gIHRvb2xfZXJyb3I/OiBzdHJpbmc7XG4gIC8vIE9sbGFtYeeUn+aIkOe7n+iuoeS/oeaBr1xuICB0b3RhbF9kdXJhdGlvbj86IG51bWJlcjtcbiAgbG9hZF9kdXJhdGlvbj86IG51bWJlcjtcbiAgcHJvbXB0X2V2YWxfY291bnQ/OiBudW1iZXI7XG4gIHByb21wdF9ldmFsX2R1cmF0aW9uPzogbnVtYmVyO1xuICBldmFsX2NvdW50PzogbnVtYmVyO1xuICBldmFsX2R1cmF0aW9uPzogbnVtYmVyO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENyZWF0ZUNvbnZlcnNhdGlvbkRhdGEge1xuICB0aXRsZTogc3RyaW5nO1xuICBtb2RlbDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENyZWF0ZU1lc3NhZ2VEYXRhIHtcbiAgY29udmVyc2F0aW9uX2lkOiBudW1iZXI7XG4gIHJvbGU6ICd1c2VyJyB8ICdhc3Npc3RhbnQnIHwgJ3N5c3RlbScgfCAndG9vbCcgfCAndG9vbF9jYWxsJyB8ICd0b29sX3Jlc3VsdCc7XG4gIGNvbnRlbnQ6IHN0cmluZztcbiAgbW9kZWw/OiBzdHJpbmc7XG4gIHNlcXVlbmNlX251bWJlcj86IG51bWJlcjtcbiAgLy8g5bel5YW36LCD55So55u45YWz5a2X5q61XG4gIHRvb2xfbmFtZT86IHN0cmluZztcbiAgdG9vbF9hcmdzPzogc3RyaW5nOyAvLyBKU09O5a2X56ym5LiyXG4gIHRvb2xfcmVzdWx0Pzogc3RyaW5nOyAvLyBKU09O5a2X56ym5LiyXG4gIHRvb2xfc3RhdHVzPzogJ2V4ZWN1dGluZycgfCAnY29tcGxldGVkJyB8ICdlcnJvcic7XG4gIHRvb2xfZXhlY3V0aW9uX3RpbWU/OiBudW1iZXI7IC8vIOavq+enklxuICB0b29sX2Vycm9yPzogc3RyaW5nO1xuICAvLyBPbGxhbWHnlJ/miJDnu5/orqHkv6Hmga9cbiAgdG90YWxfZHVyYXRpb24/OiBudW1iZXI7XG4gIGxvYWRfZHVyYXRpb24/OiBudW1iZXI7XG4gIHByb21wdF9ldmFsX2NvdW50PzogbnVtYmVyO1xuICBwcm9tcHRfZXZhbF9kdXJhdGlvbj86IG51bWJlcjtcbiAgZXZhbF9jb3VudD86IG51bWJlcjtcbiAgZXZhbF9kdXJhdGlvbj86IG51bWJlcjtcbn1cblxuLy8gTUNQ55u45YWz5o6l5Y+jXG5leHBvcnQgaW50ZXJmYWNlIE1jcFNlcnZlciB7XG4gIGlkOiBudW1iZXI7XG4gIG5hbWU6IHN0cmluZztcbiAgZGlzcGxheV9uYW1lOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xuICB0eXBlOiAnc3RkaW8nIHwgJ3NzZScgfCAnc3RyZWFtYWJsZS1odHRwJztcbiAgc3RhdHVzOiAnY29ubmVjdGVkJyB8ICdkaXNjb25uZWN0ZWQnIHwgJ2Vycm9yJyB8ICdjb25uZWN0aW5nJztcbiAgZW5hYmxlZDogYm9vbGVhbjtcbiAgXG4gIC8vIFNURElP6YWN572uXG4gIGNvbW1hbmQ/OiBzdHJpbmc7XG4gIGFyZ3M/OiBzdHJpbmc7IC8vIEpTT07mlbDnu4TmoLzlvI9cbiAgd29ya2luZ19kaXJlY3Rvcnk/OiBzdHJpbmc7XG4gIFxuICAvLyBTU0UvSFRUUOmFjee9rlxuICB1cmw/OiBzdHJpbmc7XG4gIGJhc2VfdXJsPzogc3RyaW5nO1xuICBwb3J0PzogbnVtYmVyO1xuICBwYXRoPzogc3RyaW5nO1xuICBwcm90b2NvbD86ICdodHRwJyB8ICdodHRwcyc7XG4gIFxuICAvLyDpgJrnlKjphY3nva5cbiAgaGVhZGVycz86IHN0cmluZzsgLy8gSlNPTuWvueixoeagvOW8j1xuICBhdXRoX3R5cGU/OiAnbm9uZScgfCAnYmVhcmVyJyB8ICdiYXNpYycgfCAnYXBpX2tleSc7XG4gIGF1dGhfY29uZmlnPzogc3RyaW5nOyAvLyBKU09O5qC85byPXG4gIHRpbWVvdXRfbXM/OiBudW1iZXI7XG4gIHJldHJ5X2F0dGVtcHRzPzogbnVtYmVyO1xuICByZXRyeV9kZWxheV9tcz86IG51bWJlcjtcbiAgXG4gIC8vIOaJqeWxlemFjee9rlxuICBleHRyYV9jb25maWc/OiBzdHJpbmc7IC8vIEpTT07moLzlvI9cbiAgXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xuICBsYXN0X2Nvbm5lY3RlZF9hdD86IHN0cmluZztcbiAgZXJyb3JfbWVzc2FnZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBNY3BUb29sIHtcbiAgaWQ6IG51bWJlcjtcbiAgc2VydmVyX2lkOiBudW1iZXI7XG4gIG5hbWU6IHN0cmluZztcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIGlucHV0X3NjaGVtYT86IHN0cmluZzsgLy8gSlNPTuagvOW8j1xuICBpc19hdmFpbGFibGU6IGJvb2xlYW47XG4gIGVuYWJsZWQ6IGJvb2xlYW47XG4gIGxhc3RfdXNlZF9hdD86IHN0cmluZztcbiAgdXNhZ2VfY291bnQ6IG51bWJlcjtcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xuICB1cGRhdGVkX2F0OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTWNwVG9vbENhbGwge1xuICBpZDogbnVtYmVyO1xuICB0b29sX2lkOiBudW1iZXI7XG4gIGNvbnZlcnNhdGlvbl9pZDogbnVtYmVyO1xuICBtZXNzYWdlX2lkPzogbnVtYmVyO1xuICBpbnB1dF9hcmdzPzogc3RyaW5nOyAvLyBKU09O5qC85byPXG4gIG91dHB1dF9yZXN1bHQ/OiBzdHJpbmc7IC8vIEpTT07moLzlvI9cbiAgZXhlY3V0aW9uX3RpbWVfbXM/OiBudW1iZXI7XG4gIHN0YXR1czogJ3N1Y2Nlc3MnIHwgJ2Vycm9yJyB8ICd0aW1lb3V0JztcbiAgZXJyb3JfbWVzc2FnZT86IHN0cmluZztcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENyZWF0ZU1jcFNlcnZlckRhdGEge1xuICBuYW1lOiBzdHJpbmc7XG4gIGRpc3BsYXlfbmFtZTogc3RyaW5nO1xuICBkZXNjcmlwdGlvbj86IHN0cmluZztcbiAgdHlwZTogJ3N0ZGlvJyB8ICdzc2UnIHwgJ3N0cmVhbWFibGUtaHR0cCc7XG4gIGVuYWJsZWQ/OiBib29sZWFuO1xuICBcbiAgLy8gU1RESU/phY3nva5cbiAgY29tbWFuZD86IHN0cmluZztcbiAgYXJncz86IHN0cmluZ1tdO1xuICB3b3JraW5nX2RpcmVjdG9yeT86IHN0cmluZztcbiAgXG4gIC8vIFNTRS9IVFRQ6YWN572uXG4gIHVybD86IHN0cmluZztcbiAgYmFzZV91cmw/OiBzdHJpbmc7XG4gIHBvcnQ/OiBudW1iZXI7XG4gIHBhdGg/OiBzdHJpbmc7XG4gIHByb3RvY29sPzogJ2h0dHAnIHwgJ2h0dHBzJztcbiAgXG4gIC8vIOmAmueUqOmFjee9rlxuICBoZWFkZXJzPzogUmVjb3JkPHN0cmluZywgc3RyaW5nPjtcbiAgYXV0aF90eXBlPzogJ25vbmUnIHwgJ2JlYXJlcicgfCAnYmFzaWMnIHwgJ2FwaV9rZXknO1xuICBhdXRoX2NvbmZpZz86IFJlY29yZDxzdHJpbmcsIGFueT47XG4gIHRpbWVvdXRfbXM/OiBudW1iZXI7XG4gIHJldHJ5X2F0dGVtcHRzPzogbnVtYmVyO1xuICByZXRyeV9kZWxheV9tcz86IG51bWJlcjtcbiAgXG4gIC8vIOaJqeWxlemFjee9rlxuICBleHRyYV9jb25maWc/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENyZWF0ZU1jcFRvb2xEYXRhIHtcbiAgc2VydmVyX2lkOiBudW1iZXI7XG4gIG5hbWU6IHN0cmluZztcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIGlucHV0X3NjaGVtYT86IFJlY29yZDxzdHJpbmcsIGFueT47XG4gIGlzX2F2YWlsYWJsZT86IGJvb2xlYW47XG4gIGVuYWJsZWQ/OiBib29sZWFuO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENyZWF0ZU1jcFRvb2xDYWxsRGF0YSB7XG4gIHRvb2xfaWQ6IG51bWJlcjtcbiAgY29udmVyc2F0aW9uX2lkOiBudW1iZXI7XG4gIG1lc3NhZ2VfaWQ/OiBudW1iZXI7XG4gIGlucHV0X2FyZ3M/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xuICBvdXRwdXRfcmVzdWx0PzogUmVjb3JkPHN0cmluZywgYW55PjtcbiAgZXhlY3V0aW9uX3RpbWVfbXM/OiBudW1iZXI7XG4gIHN0YXR1czogJ3N1Y2Nlc3MnIHwgJ2Vycm9yJyB8ICd0aW1lb3V0JztcbiAgZXJyb3JfbWVzc2FnZT86IHN0cmluZztcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/types.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconversations%2F%5Bid%5D%2Froute&page=%2Fapi%2Fconversations%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconversations%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();