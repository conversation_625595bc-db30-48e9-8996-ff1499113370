"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _default = exports["default"] = {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "background": "#fff",
    "color": "#000"
  },
  "xml .hljs-meta": {
    "fontWeight": "bold",
    "fontStyle": "italic",
    "color": "#48b"
  },
  "hljs-comment": {
    "fontStyle": "italic",
    "color": "#070"
  },
  "hljs-quote": {
    "fontStyle": "italic",
    "color": "#070"
  },
  "hljs-name": {
    "color": "#808",
    "fontWeight": "bold"
  },
  "hljs-keyword": {
    "color": "#808"
  },
  "hljs-attr": {
    "fontWeight": "bold",
    "color": "#000"
  },
  "hljs-string": {
    "fontWeight": "normal",
    "color": "#00f"
  },
  "hljs-variable": {
    "color": "#477"
  },
  "hljs-template-variable": {
    "color": "#477"
  },
  "hljs-code": {
    "color": "#00f"
  },
  "hljs-meta-string": {
    "color": "#00f"
  },
  "hljs-number": {
    "color": "#00f"
  },
  "hljs-regexp": {
    "color": "#00f"
  },
  "hljs-link": {
    "color": "#00f"
  },
  "hljs-title": {
    "color": "#f40"
  },
  "hljs-symbol": {
    "color": "#f40"
  },
  "hljs-bullet": {
    "color": "#f40"
  },
  "hljs-built_in": {
    "color": "#f40"
  },
  "hljs-builtin-name": {
    "color": "#f40"
  },
  "hljs-section": {
    "color": "#642"
  },
  "hljs-meta": {
    "color": "#642"
  },
  "hljs-class .hljs-title": {
    "color": "#639"
  },
  "hljs-type": {
    "color": "#639"
  },
  "hljs-function .hljs-title": {
    "color": "#000"
  },
  "hljs-subst": {
    "color": "#000"
  },
  "hljs-formula": {
    "backgroundColor": "#eee",
    "fontStyle": "italic"
  },
  "hljs-addition": {
    "backgroundColor": "#beb"
  },
  "hljs-deletion": {
    "backgroundColor": "#fbb"
  },
  "hljs-selector-id": {
    "color": "#964"
  },
  "hljs-selector-class": {
    "color": "#964"
  },
  "hljs-doctag": {
    "fontWeight": "bold"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  }
};