'use client';

import React, { useEffect, useState, useRef } from 'react';
import { MarkdownRenderer } from './MarkdownRenderer';

interface StreamingMarkdownRendererProps {
  content: string;
  isStreaming?: boolean;
  className?: string;
}

export function StreamingMarkdownRenderer({ 
  content, 
  isStreaming = false, 
  className = '' 
}: StreamingMarkdownRendererProps) {
  const [displayContent, setDisplayContent] = useState('');
  const [showCursor, setShowCursor] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const lastContentLength = useRef(0);

  // 处理内容更新
  useEffect(() => {
    setDisplayContent(content);
    lastContentLength.current = content.length;
  }, [content]);

  // 处理流式状态的光标显示
  useEffect(() => {
    setShowCursor(isStreaming);
  }, [isStreaming]);

  // 移除自动滚动功能，避免干扰

  return (
    <div className={`streaming-markdown-container ${className}`}>
      {/* 主要内容 */}
      <MarkdownRenderer content={displayContent} />

      {/* 简单的流式光标 - 只在真正流式时显示 */}
      {showCursor && isStreaming && (
        <span className="inline-block w-0.5 h-4 bg-blue-500 ml-1 animate-pulse" />
      )}
    </div>
  );
}
