'use client';

import React, { useEffect, useState, useRef } from 'react';
import { MarkdownRenderer } from './MarkdownRenderer';

interface StreamingMarkdownRendererProps {
  content: string;
  isStreaming?: boolean;
  className?: string;
}

export function StreamingMarkdownRenderer({ 
  content, 
  isStreaming = false, 
  className = '' 
}: StreamingMarkdownRendererProps) {
  const [displayContent, setDisplayContent] = useState('');
  const [showCursor, setShowCursor] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const lastContentLength = useRef(0);

  // 处理内容更新和光标显示
  useEffect(() => {
    if (content !== displayContent) {
      setDisplayContent(content);
      
      // 如果内容增加了，显示光标
      if (content.length > lastContentLength.current) {
        setShowCursor(true);
        lastContentLength.current = content.length;
        
        // 短暂显示光标后隐藏
        const timer = setTimeout(() => {
          setShowCursor(false);
        }, 500);
        
        return () => clearTimeout(timer);
      }
    }
  }, [content, displayContent]);

  // 处理流式状态的光标闪烁
  useEffect(() => {
    if (isStreaming) {
      setShowCursor(true);
      const interval = setInterval(() => {
        setShowCursor(prev => !prev);
      }, 600);
      
      return () => clearInterval(interval);
    } else {
      setShowCursor(false);
    }
  }, [isStreaming]);

  // 自动滚动到新内容
  useEffect(() => {
    if (contentRef.current && content.length > lastContentLength.current) {
      const element = contentRef.current;
      const isNearBottom = element.scrollTop + element.clientHeight >= element.scrollHeight - 100;
      
      if (isNearBottom) {
        setTimeout(() => {
          element.scrollTop = element.scrollHeight;
        }, 50);
      }
    }
  }, [content]);

  return (
    <div 
      ref={contentRef}
      className={`streaming-markdown-container relative ${className}`}
    >
      {/* 主要内容 */}
      <div className="flex-1 min-w-0">
        <MarkdownRenderer content={displayContent} />
      </div>
      
      {/* 流式光标 */}
      {showCursor && (
        <span 
          className={`inline-block w-2 h-5 bg-blue-500 ml-1 transition-opacity duration-150 ${
            showCursor ? 'opacity-100' : 'opacity-0'
          }`}
          style={{
            animation: isStreaming ? 'pulse 1.2s infinite' : 'none'
          }}
        />
      )}
      
      {/* CSS动画定义 */}
      <style jsx>{`
        @keyframes pulse {
          0%, 50% {
            opacity: 1;
          }
          51%, 100% {
            opacity: 0.3;
          }
        }
        
        .streaming-markdown-container {
          /* 确保内容能够平滑地出现 */
          overflow-anchor: auto;
        }
        
        /* 为新增内容添加淡入效果 */
        .streaming-markdown-container :global(.markdown-content > *:last-child) {
          animation: fadeInUp 0.3s ease-out;
        }
        
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        /* 为代码块添加特殊的出现动画 */
        .streaming-markdown-container :global(pre) {
          animation: slideInFromLeft 0.4s ease-out;
        }
        
        @keyframes slideInFromLeft {
          from {
            opacity: 0;
            transform: translateX(-20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
        
        /* 为列表项添加逐个出现的效果 */
        .streaming-markdown-container :global(li) {
          animation: fadeInScale 0.3s ease-out;
        }
        
        @keyframes fadeInScale {
          from {
            opacity: 0;
            transform: scale(0.95);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
        
        /* 为表格添加展开效果 */
        .streaming-markdown-container :global(table) {
          animation: expandIn 0.5s ease-out;
        }
        
        @keyframes expandIn {
          from {
            opacity: 0;
            transform: scaleY(0.8);
          }
          to {
            opacity: 1;
            transform: scaleY(1);
          }
        }
        
        /* 为标题添加打字机效果 */
        .streaming-markdown-container :global(h1, h2, h3, h4, h5, h6) {
          animation: typewriter 0.6s ease-out;
        }
        
        @keyframes typewriter {
          from {
            width: 0;
            opacity: 0;
          }
          to {
            width: 100%;
            opacity: 1;
          }
        }
        
        /* 为引用块添加滑入效果 */
        .streaming-markdown-container :global(blockquote) {
          animation: slideInFromRight 0.4s ease-out;
        }
        
        @keyframes slideInFromRight {
          from {
            opacity: 0;
            transform: translateX(20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
      `}</style>
    </div>
  );
}
