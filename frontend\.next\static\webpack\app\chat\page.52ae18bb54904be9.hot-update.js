"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/MessageItem.tsx":
/*!*************************************************!*\
  !*** ./src/app/chat/components/MessageItem.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageItem: () => (/* binding */ MessageItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AIStatusIndicator */ \"(app-pages-browser)/./src/app/chat/components/AIStatusIndicator.tsx\");\n/* harmony import */ var _ThinkingMode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ThinkingMode */ \"(app-pages-browser)/./src/app/chat/components/ThinkingMode.tsx\");\n/* harmony import */ var _MarkdownRenderer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MarkdownRenderer */ \"(app-pages-browser)/./src/app/chat/components/MarkdownRenderer.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageItem auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MessageItem(param) {\n    let { message, messageIndex, showAIStatus, aiState, activeToolCalls, thinkingStartTime } = param;\n    var _this = this;\n    _s();\n    const [isThinkingExpanded, setIsThinkingExpanded] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const isUser = message.role === 'user';\n    const isAssistant = message.role === 'assistant';\n    const isTool = message.role === 'tool';\n    const isToolCall = message.role === 'tool_call';\n    // tool_call类型的消息现在由独立的ToolCallMessage组件处理，这里不再渲染\n    if (isToolCall) {\n        return null;\n    }\n    // 检测并渲染图片URL（用于工具调用结果）\n    const renderImageIfUrl = (text)=>{\n        const imageUrlRegex = /(https?:\\/\\/[^\\s]+\\.(jpg|jpeg|png|gif|webp|svg))/gi;\n        const matches = text.match(imageUrlRegex);\n        if (matches) {\n            const parts = text.split(imageUrlRegex);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: parts.map((part, index)=>{\n                    if (imageUrlRegex.test(part)) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: part,\n                                    alt: \"工具返回的图片\",\n                                    className: \"max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600\",\n                                    onError: (e)=>{\n                                        var _e_currentTarget_nextElementSibling;\n                                        e.currentTarget.style.display = 'none';\n                                        (_e_currentTarget_nextElementSibling = e.currentTarget.nextElementSibling) === null || _e_currentTarget_nextElementSibling === void 0 ? void 0 : _e_currentTarget_nextElementSibling.classList.remove('hidden');\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden text-sm text-gray-500 italic\",\n                                    children: [\n                                        \"图片加载失败: \",\n                                        part\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 17\n                        }, this);\n                    }\n                    return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: part\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 27\n                    }, this) : null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 69,\n            columnNumber: 12\n        }, this);\n    };\n    // 渲染Markdown内容（用于助手消息）\n    const renderMarkdownContent = function(content) {\n        let isStreaming = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        // 临时测试：直接使用MarkdownRenderer看看是否工作\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1rem'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_4__.MarkdownRenderer, {\n                    content: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, _this),\n                isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-block w-0.5 h-4 bg-blue-500 ml-1 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, _this);\n    };\n    // 渲染工具调用消息\n    const renderToolCallContent = ()=>{\n        if (!isToolCall) return null;\n        // 解析工具相关数据\n        const toolName = message.tool_name || '未知工具';\n        const toolArgs = message.tool_args ? JSON.parse(message.tool_args) : {};\n        const toolResult = message.tool_result ? JSON.parse(message.tool_result) : null;\n        const toolStatus = message.tool_status || 'executing';\n        const toolError = message.tool_error;\n        const executionTime = message.tool_execution_time;\n        const getStatusColor = ()=>{\n            switch(toolStatus){\n                case 'executing':\n                    return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-200';\n                case 'completed':\n                    return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200';\n                case 'error':\n                    return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200';\n                default:\n                    return 'bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 text-gray-800 dark:text-gray-200';\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium mb-1\",\n                            children: [\n                                \"\\uD83D\\uDD27 \",\n                                toolName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs opacity-75\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"参数：\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"mt-1 text-xs bg-black/5 dark:bg-white/5 p-2 rounded overflow-x-auto\",\n                                    children: JSON.stringify(toolArgs, null, 2)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this),\n                toolStatus === 'executing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-blue-600 dark:text-blue-400\",\n                    children: \"⏳ 正在执行...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this),\n                toolStatus === 'error' && toolError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-red-800 dark:text-red-200 mb-1\",\n                            children: \"错误信息：\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-red-700 dark:text-red-300 whitespace-pre-wrap\",\n                            children: toolError\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, this),\n                toolStatus === 'completed' && toolResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-800 dark:text-gray-200 mb-1\",\n                            children: \"执行结果：\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-700 dark:text-gray-300 max-h-96 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: renderImageIfUrl(typeof toolResult === 'string' ? toolResult : JSON.stringify(toolResult, null, 2))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, this),\n                executionTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\n                        \"执行时间: \",\n                        executionTime,\n                        \"ms\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this);\n    };\n    // 工具调用结果现在由ToolCallMessage组件处理，这里不再显示\n    const renderToolContent = (_content)=>{\n        // 工具消息现在由独立的ToolCallMessage组件处理，这里返回空内容\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-gray-500 italic text-sm\",\n            children: \"工具调用结果由独立组件显示\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 160,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理助手消息内容（智能清理工具调用相关逻辑）\n    const renderAssistantContent = (content)=>{\n        // 智能清理工具调用相关的内容，只清理type为'function'的工具调用\n        let cleanContent = content;\n        // 检测并清理各种工具调用格式\n        const functionToolCallRegex = /```json\\n{[\\s\\S]*?\"type\":\\s*[\"']function[\"'][\\s\\S]*?}\\n```/g;\n        const mcpToolCallRegex = /调用工具:\\s*.*?\\n参数:\\s*[\\s\\S]*?\\n\\n(?:结果|错误|状态):\\s*[\\s\\S]*?(?=\\n\\n|$)/g;\n        // 清理function类型的工具调用JSON格式\n        cleanContent = cleanContent.replace(functionToolCallRegex, '');\n        // 清理MCP工具调用格式\n        cleanContent = cleanContent.replace(mcpToolCallRegex, '');\n        // 清理其他工具调用相关的状态信息\n        cleanContent = cleanContent.replace(/工具调用完成[\\s\\S]*?(?=\\n\\n|$)/g, '').replace(/工具执行结果[\\s\\S]*?(?=\\n\\n|$)/g, '').replace(/🔧\\s*.*?\\n[\\s\\S]*?(?=\\n\\n|$)/g, '') // 清理工具图标开头的内容\n        .replace(/执行工具[\\s\\S]*?(?=\\n\\n|$)/g, '').replace(/Tool\\s+call[\\s\\S]*?(?=\\n\\n|$)/gi, '').replace(/\\n{3,}/g, '\\n\\n') // 清理多余的换行\n        .trim();\n        // 检查是否包含思考内容或正在思考中\n        const hasThinking = (0,_ThinkingMode__WEBPACK_IMPORTED_MODULE_3__.hasThinkingContent)(cleanContent);\n        const isCurrentlyThinking = showAIStatus && (aiState === null || aiState === void 0 ? void 0 : aiState.status) === 'thinking';\n        const isGeneratingWithThinking = showAIStatus && (aiState === null || aiState === void 0 ? void 0 : aiState.status) === 'generating' && hasThinking;\n        // 检测思考状态：不仅依赖aiState，也要检查内容中的思考标签\n        const hasThinkingInProgress = /<think>/.test(cleanContent) && !/<\\/think>/.test(cleanContent);\n        const isThinkingAnyway = hasThinkingInProgress || isCurrentlyThinking || isGeneratingWithThinking;\n        // 移除思考标签，获取清理后的内容用于显示\n        const contentWithoutThinking = (0,_ThinkingMode__WEBPACK_IMPORTED_MODULE_3__.removeThinkingContent)(cleanContent);\n        // 如果正在思考但还没有内容，仍然要显示思考面板\n        const shouldShow = contentWithoutThinking || isThinkingAnyway || hasThinking;\n        if (!shouldShow) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '0.75rem'\n            },\n            children: [\n                (hasThinking || isThinkingAnyway) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThinkingMode__WEBPACK_IMPORTED_MODULE_3__.ThinkingMode, {\n                        content: cleanContent,\n                        isExpanded: isThinkingExpanded,\n                        onToggleExpand: ()=>setIsThinkingExpanded(!isThinkingExpanded),\n                        defaultHidden: false\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, this),\n                contentWithoutThinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        flex: 1\n                    },\n                    children: renderMarkdownContent(contentWithoutThinking, showAIStatus && ((aiState === null || aiState === void 0 ? void 0 : aiState.status) === 'generating' || (aiState === null || aiState === void 0 ? void 0 : aiState.status) === 'thinking'))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, this);\n    };\n    // 格式化时间（毫秒转秒）\n    const formatDuration = (nanoseconds)=>{\n        if (!nanoseconds) return null;\n        const seconds = (nanoseconds / 1000000000).toFixed(2);\n        return \"\".concat(seconds, \"s\");\n    };\n    const renderGenerationStatsIcon = ()=>{\n        // 根据是否有统计数据显示不同的悬浮内容\n        const statsText = message.total_duration ? \"生成时间: \".concat((message.total_duration / 1000000).toFixed(2), \"ms\\n\") + \"提示词处理: \".concat(message.prompt_eval_count || 0, \" tokens\\n\") + \"生成内容: \".concat(message.eval_count || 0, \" tokens\\n\") + \"提示词速度: \".concat(message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\\n\") + \"生成速度: \".concat(message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\") : '正在生成中，统计信息将在完成后显示...';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative group\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-0 bottom-5 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 min-w-max\",\n                    children: statsText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染生成统计信息\n    const renderGenerationStats = ()=>{\n        if (!isAssistant || !message.total_duration) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-2 text-xs text-gray-500 dark:text-gray-400 space-y-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"总时长: \",\n                                formatDuration(message.total_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"加载时长: \",\n                                formatDuration(message.load_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"提示评估: \",\n                                message.prompt_eval_count,\n                                \" tokens (\",\n                                formatDuration(message.prompt_eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"生成: \",\n                                message.eval_count,\n                                \" tokens (\",\n                                formatDuration(message.eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 263,\n            columnNumber: 7\n        }, this);\n    };\n    // 对于助手消息，检查是否应该显示\n    if (isAssistant) {\n        const assistantContent = renderAssistantContent(message.content);\n        if (!assistantContent) {\n            return null; // 不渲染空的助手消息\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 \".concat(isUser ? 'justify-end' : 'justify-start'),\n        children: [\n            !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 \".concat(isTool ? 'bg-orange-600' : isToolCall ? 'bg-purple-600' : 'bg-blue-600'),\n                            children: isTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 17\n                            }, this) : isToolCall ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this),\n                        showAIStatus && aiState && aiState.status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__.AIStatusIndicator, {\n                                aiState: aiState\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 287,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col max-w-[70%]\",\n                children: [\n                    !isUser && isAssistant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                children: message.model || '加载中...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this),\n                            renderGenerationStatsIcon()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg px-4 py-3 \".concat(isUser ? 'bg-theme-primary text-white' : isTool ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200 border border-orange-300 dark:border-orange-700' : isToolCall ? 'bg-theme-background-tertiary border border-theme-border text-theme-foreground' : 'bg-theme-card border border-theme-border text-theme-foreground'),\n                        style: {\n                            display: 'flex',\n                            flexDirection: 'column',\n                            gap: '0.75rem'\n                        },\n                        children: isToolCall ? renderToolCallContent() : isTool ? renderToolContent(message.content) : isAssistant ? renderAssistantContent(message.content) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: renderMarkdownContent(message.content, false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this),\n            isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 text-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n        lineNumber: 285,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageItem, \"3x2ATlWde6afHsaQQJRexlrfMGQ=\");\n_c = MessageItem;\nvar _c;\n$RefreshReg$(_c, \"MessageItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/MessageItem.tsx\n"));

/***/ })

});