"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _default = exports["default"] = {
  "hljs": {
    "display": "block",
    "background": "white",
    "padding": "0.5em",
    "color": "#333333",
    "overflowX": "auto"
  },
  "hljs-comment": {
    "color": "#969896"
  },
  "hljs-meta": {
    "color": "#969896"
  },
  "hljs-variable": {
    "color": "#df5000"
  },
  "hljs-template-variable": {
    "color": "#df5000"
  },
  "hljs-strong": {
    "color": "#df5000"
  },
  "hljs-emphasis": {
    "color": "#df5000"
  },
  "hljs-quote": {
    "color": "#df5000"
  },
  "hljs-keyword": {
    "color": "#d73a49"
  },
  "hljs-selector-tag": {
    "color": "#d73a49"
  },
  "hljs-type": {
    "color": "#d73a49"
  },
  "hljs-literal": {
    "color": "#0086b3"
  },
  "hljs-symbol": {
    "color": "#0086b3"
  },
  "hljs-bullet": {
    "color": "#0086b3"
  },
  "hljs-attribute": {
    "color": "#0086b3"
  },
  "hljs-section": {
    "color": "#63a35c"
  },
  "hljs-name": {
    "color": "#63a35c"
  },
  "hljs-tag": {
    "color": "#333333"
  },
  "hljs-title": {
    "color": "#6f42c1"
  },
  "hljs-attr": {
    "color": "#6f42c1"
  },
  "hljs-selector-id": {
    "color": "#6f42c1"
  },
  "hljs-selector-class": {
    "color": "#6f42c1"
  },
  "hljs-selector-attr": {
    "color": "#6f42c1"
  },
  "hljs-selector-pseudo": {
    "color": "#6f42c1"
  },
  "hljs-addition": {
    "color": "#55a532",
    "backgroundColor": "#eaffea"
  },
  "hljs-deletion": {
    "color": "#bd2c00",
    "backgroundColor": "#ffecec"
  },
  "hljs-link": {
    "textDecoration": "underline"
  },
  "hljs-number": {
    "color": "#005cc5"
  },
  "hljs-string": {
    "color": "#032f62"
  }
};