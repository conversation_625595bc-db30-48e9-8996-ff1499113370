'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { useTheme } from '@/theme/contexts/ThemeContext';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // 自定义代码块渲染组件
  const CodeBlock = ({ node, inline, className, children, ...props }: any) => {
    const match = /language-(\w+)/.exec(className || '');
    const language = match ? match[1] : '';

    if (inline) {
      // 内联代码
      return (
        <code 
          className="bg-gray-100 dark:bg-gray-800 text-red-600 dark:text-red-400 px-1 py-0.5 rounded text-sm font-mono"
          {...props}
        >
          {children}
        </code>
      );
    }

    // 代码块
    return (
      <div className="my-4 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
        {language && (
          <div className="bg-gray-100 dark:bg-gray-800 px-4 py-2 text-sm text-gray-600 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
            {language}
          </div>
        )}
        <SyntaxHighlighter
          style={isDark ? oneDark : oneLight}
          language={language || 'text'}
          PreTag="div"
          customStyle={{
            margin: 0,
            padding: '1rem',
            background: 'transparent',
            fontSize: '0.875rem',
            lineHeight: '1.5',
          }}
          {...props}
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      </div>
    );
  };

  // 自定义链接渲染
  const LinkRenderer = ({ href, children, ...props }: any) => (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
      {...props}
    >
      {children}
    </a>
  );

  // 自定义表格渲染
  const TableRenderer = ({ children, ...props }: any) => (
    <div className="my-4 overflow-x-auto">
      <table className="min-w-full border border-gray-200 dark:border-gray-700 rounded-lg" {...props}>
        {children}
      </table>
    </div>
  );

  const TableHeaderRenderer = ({ children, ...props }: any) => (
    <thead className="bg-gray-50 dark:bg-gray-800" {...props}>
      {children}
    </thead>
  );

  const TableCellRenderer = ({ children, ...props }: any) => (
    <td className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 text-sm" {...props}>
      {children}
    </td>
  );

  const TableHeaderCellRenderer = ({ children, ...props }: any) => (
    <th className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 text-sm font-medium text-left" {...props}>
      {children}
    </th>
  );

  // 自定义引用块渲染
  const BlockquoteRenderer = ({ children, ...props }: any) => (
    <blockquote
      className="border-l-4 border-gray-300 dark:border-gray-600 pl-4 my-4 italic text-gray-700 dark:text-gray-300"
      {...props}
    >
      {children}
    </blockquote>
  );

  // 自定义列表渲染
  const ListRenderer = ({ ordered, children, ...props }: any) => {
    const Tag = ordered ? 'ol' : 'ul';
    return (
      <Tag
        className={`my-4 ${ordered ? 'list-decimal' : 'list-disc'} list-inside space-y-1`}
        {...props}
      >
        {children}
      </Tag>
    );
  };

  const ListItemRenderer = ({ children, ...props }: any) => (
    <li className="text-gray-800 dark:text-gray-200" {...props}>
      {children}
    </li>
  );

  // 自定义标题渲染
  const HeadingRenderer = ({ level, children, ...props }: any) => {
    const Tag = `h${level}` as keyof JSX.IntrinsicElements;
    const sizeClasses = {
      1: 'text-2xl font-bold mt-6 mb-4',
      2: 'text-xl font-bold mt-5 mb-3',
      3: 'text-lg font-bold mt-4 mb-2',
      4: 'text-base font-bold mt-3 mb-2',
      5: 'text-sm font-bold mt-2 mb-1',
      6: 'text-xs font-bold mt-2 mb-1',
    };

    return (
      <Tag
        className={`${sizeClasses[level as keyof typeof sizeClasses]} text-gray-900 dark:text-gray-100`}
        {...props}
      >
        {children}
      </Tag>
    );
  };

  // 自定义段落渲染
  const ParagraphRenderer = ({ children, ...props }: any) => (
    <p className="my-2 text-gray-800 dark:text-gray-200 leading-relaxed" {...props}>
      {children}
    </p>
  );

  return (
    <div className={`markdown-content ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={{
          code: CodeBlock,
          a: LinkRenderer,
          table: TableRenderer,
          thead: TableHeaderRenderer,
          td: TableCellRenderer,
          th: TableHeaderCellRenderer,
          blockquote: BlockquoteRenderer,
          ul: ListRenderer,
          ol: ListRenderer,
          li: ListItemRenderer,
          h1: HeadingRenderer,
          h2: HeadingRenderer,
          h3: HeadingRenderer,
          h4: HeadingRenderer,
          h5: HeadingRenderer,
          h6: HeadingRenderer,
          p: ParagraphRenderer,
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}
