'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';

interface SimpleMarkdownTestProps {
  content: string;
}

export function SimpleMarkdownTest({ content }: SimpleMarkdownTestProps) {
  console.log('Rendering markdown content:', content);
  
  return (
    <div style={{ 
      border: '1px solid red', 
      padding: '1rem', 
      margin: '1rem 0',
      backgroundColor: '#f9f9f9'
    }}>
      <h4 style={{ color: 'red', marginBottom: '0.5rem' }}>Markdown Test:</h4>
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        gap: '1rem',
        backgroundColor: 'white',
        padding: '1rem',
        border: '1px solid #ccc'
      }}>
        <ReactMarkdown>
          {content}
        </ReactMarkdown>
      </div>
      <h4 style={{ color: 'blue', marginTop: '1rem', marginBottom: '0.5rem' }}>Raw Content:</h4>
      <pre style={{ 
        backgroundColor: '#eee', 
        padding: '0.5rem', 
        fontSize: '0.8rem',
        whiteSpace: 'pre-wrap'
      }}>
        {content}
      </pre>
    </div>
  );
}
