/**
 * localStorage 工具函数
 * 用于持久化存储聊天状态
 */

const STORAGE_KEYS = {
  CURRENT_CONVERSATION_ID: 'kun-agent-current-conversation-id',
  SELECTED_MODEL: 'kun-agent-selected-model',
  ENABLE_TOOLS: 'kun-agent-enable-tools',
  SELECTED_TOOLS: 'kun-agent-selected-tools',
} as const;

// 安全的localStorage操作，处理SSR和错误情况
const safeLocalStorage = {
  getItem: (key: string): string | null => {
    if (typeof window === 'undefined') return null;
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.warn('localStorage getItem error:', error);
      return null;
    }
  },

  setItem: (key: string, value: string): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.warn('localStorage setItem error:', error);
    }
  },

  removeItem: (key: string): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('localStorage removeItem error:', error);
    }
  },
};

// 聊天状态存储接口
export interface ChatState {
  currentConversationId: number | null;
  selectedModel: string;
  enableTools: boolean;
  selectedTools: string[];
}

// 保存当前对话ID
export const saveCurrentConversationId = (conversationId: number | null): void => {
  if (conversationId === null) {
    safeLocalStorage.removeItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID);
  } else {
    safeLocalStorage.setItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID, String(conversationId));
  }
};

// 获取当前对话ID
export const getCurrentConversationId = (): number | null => {
  const stored = safeLocalStorage.getItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID);
  return stored ? Number(stored) : null;
};

// 保存选中的模型
export const saveSelectedModel = (model: string): void => {
  safeLocalStorage.setItem(STORAGE_KEYS.SELECTED_MODEL, model);
};

// 获取选中的模型
export const getSelectedModel = (): string | null => {
  return safeLocalStorage.getItem(STORAGE_KEYS.SELECTED_MODEL);
};

// 保存工具开关状态
export const saveEnableTools = (enabled: boolean): void => {
  safeLocalStorage.setItem(STORAGE_KEYS.ENABLE_TOOLS, String(enabled));
};

// 获取工具开关状态
export const getEnableTools = (): boolean => {
  const stored = safeLocalStorage.getItem(STORAGE_KEYS.ENABLE_TOOLS);
  return stored === 'true';
};

// 保存选中的工具列表
export const saveSelectedTools = (tools: string[]): void => {
  safeLocalStorage.setItem(STORAGE_KEYS.SELECTED_TOOLS, JSON.stringify(tools));
};

// 获取选中的工具列表
export const getSelectedTools = (): string[] => {
  const stored = safeLocalStorage.getItem(STORAGE_KEYS.SELECTED_TOOLS);
  try {
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.warn('Failed to parse selected tools from localStorage:', error);
    return [];
  }
};

// 保存完整的聊天状态
export const saveChatState = (state: Partial<ChatState>): void => {
  if (state.currentConversationId !== undefined) {
    saveCurrentConversationId(state.currentConversationId);
  }
  if (state.selectedModel !== undefined) {
    saveSelectedModel(state.selectedModel);
  }
  if (state.enableTools !== undefined) {
    saveEnableTools(state.enableTools);
  }
  if (state.selectedTools !== undefined) {
    saveSelectedTools(state.selectedTools);
  }
};

// 获取完整的聊天状态
export const getChatState = (): Partial<ChatState> => {
  return {
    currentConversationId: getCurrentConversationId(),
    selectedModel: getSelectedModel(),
    enableTools: getEnableTools(),
    selectedTools: getSelectedTools(),
  };
};

// 清除所有聊天状态
export const clearChatState = (): void => {
  Object.values(STORAGE_KEYS).forEach(key => {
    safeLocalStorage.removeItem(key);
  });
};
