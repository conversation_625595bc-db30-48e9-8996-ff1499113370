"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/components/MessageItem.tsx":
/*!*************************************************!*\
  !*** ./src/app/chat/components/MessageItem.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MessageItem: () => (/* binding */ MessageItem)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Info,User,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AIStatusIndicator */ \"(app-pages-browser)/./src/app/chat/components/AIStatusIndicator.tsx\");\n/* harmony import */ var _ThinkingMode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ThinkingMode */ \"(app-pages-browser)/./src/app/chat/components/ThinkingMode.tsx\");\n/* harmony import */ var _MarkdownRenderer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./MarkdownRenderer */ \"(app-pages-browser)/./src/app/chat/components/MarkdownRenderer.tsx\");\n/* __next_internal_client_entry_do_not_use__ MessageItem auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MessageItem(param) {\n    let { message, messageIndex, showAIStatus, aiState, activeToolCalls, thinkingStartTime } = param;\n    _s();\n    const [isThinkingExpanded, setIsThinkingExpanded] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const isUser = message.role === 'user';\n    const isAssistant = message.role === 'assistant';\n    const isTool = message.role === 'tool';\n    const isToolCall = message.role === 'tool_call';\n    // tool_call类型的消息现在由独立的ToolCallMessage组件处理，这里不再渲染\n    if (isToolCall) {\n        return null;\n    }\n    // 检测并渲染图片URL（用于工具调用结果）\n    const renderImageIfUrl = (text)=>{\n        const imageUrlRegex = /(https?:\\/\\/[^\\s]+\\.(jpg|jpeg|png|gif|webp|svg))/gi;\n        const matches = text.match(imageUrlRegex);\n        if (matches) {\n            const parts = text.split(imageUrlRegex);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: parts.map((part, index)=>{\n                    if (imageUrlRegex.test(part)) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: part,\n                                    alt: \"工具返回的图片\",\n                                    className: \"max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-600\",\n                                    onError: (e)=>{\n                                        var _e_currentTarget_nextElementSibling;\n                                        e.currentTarget.style.display = 'none';\n                                        (_e_currentTarget_nextElementSibling = e.currentTarget.nextElementSibling) === null || _e_currentTarget_nextElementSibling === void 0 ? void 0 : _e_currentTarget_nextElementSibling.classList.remove('hidden');\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden text-sm text-gray-500 italic\",\n                                    children: [\n                                        \"图片加载失败: \",\n                                        part\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 17\n                        }, this);\n                    }\n                    return part ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: part\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 27\n                    }, this) : null;\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: text\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 67,\n            columnNumber: 12\n        }, this);\n    };\n    // 渲染Markdown内容（用于助手消息）\n    const renderMarkdownContent = (content)=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MarkdownRenderer__WEBPACK_IMPORTED_MODULE_4__.MarkdownRenderer, {\n            content: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 72,\n            columnNumber: 12\n        }, this);\n    };\n    // 渲染工具调用消息\n    const renderToolCallContent = ()=>{\n        if (!isToolCall) return null;\n        // 解析工具相关数据\n        const toolName = message.tool_name || '未知工具';\n        const toolArgs = message.tool_args ? JSON.parse(message.tool_args) : {};\n        const toolResult = message.tool_result ? JSON.parse(message.tool_result) : null;\n        const toolStatus = message.tool_status || 'executing';\n        const toolError = message.tool_error;\n        const executionTime = message.tool_execution_time;\n        const getStatusColor = ()=>{\n            switch(toolStatus){\n                case 'executing':\n                    return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-200';\n                case 'completed':\n                    return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200';\n                case 'error':\n                    return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200';\n                default:\n                    return 'bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700 text-gray-800 dark:text-gray-200';\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium mb-1\",\n                            children: [\n                                \"\\uD83D\\uDD27 \",\n                                toolName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs opacity-75\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"参数：\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"mt-1 text-xs bg-black/5 dark:bg-white/5 p-2 rounded overflow-x-auto\",\n                                    children: JSON.stringify(toolArgs, null, 2)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                toolStatus === 'executing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-blue-600 dark:text-blue-400\",\n                    children: \"⏳ 正在执行...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, this),\n                toolStatus === 'error' && toolError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-red-800 dark:text-red-200 mb-1\",\n                            children: \"错误信息：\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-red-700 dark:text-red-300 whitespace-pre-wrap\",\n                            children: toolError\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this),\n                toolStatus === 'completed' && toolResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm font-medium text-gray-800 dark:text-gray-200 mb-1\",\n                            children: \"执行结果：\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-700 dark:text-gray-300 max-h-96 overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"whitespace-pre-wrap\",\n                                children: renderImageIfUrl(typeof toolResult === 'string' ? toolResult : JSON.stringify(toolResult, null, 2))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this),\n                executionTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\n                        \"执行时间: \",\n                        executionTime,\n                        \"ms\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    };\n    // 工具调用结果现在由ToolCallMessage组件处理，这里不再显示\n    const renderToolContent = (_content)=>{\n        // 工具消息现在由独立的ToolCallMessage组件处理，这里返回空内容\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-gray-500 italic text-sm\",\n            children: \"工具调用结果由独立组件显示\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 150,\n            columnNumber: 12\n        }, this);\n    };\n    // 处理助手消息内容（智能清理工具调用相关逻辑）\n    const renderAssistantContent = (content)=>{\n        // 智能清理工具调用相关的内容，只清理type为'function'的工具调用\n        let cleanContent = content;\n        // 检测并清理各种工具调用格式\n        const functionToolCallRegex = /```json\\n{[\\s\\S]*?\"type\":\\s*[\"']function[\"'][\\s\\S]*?}\\n```/g;\n        const mcpToolCallRegex = /调用工具:\\s*.*?\\n参数:\\s*[\\s\\S]*?\\n\\n(?:结果|错误|状态):\\s*[\\s\\S]*?(?=\\n\\n|$)/g;\n        // 清理function类型的工具调用JSON格式\n        cleanContent = cleanContent.replace(functionToolCallRegex, '');\n        // 清理MCP工具调用格式\n        cleanContent = cleanContent.replace(mcpToolCallRegex, '');\n        // 清理其他工具调用相关的状态信息\n        cleanContent = cleanContent.replace(/工具调用完成[\\s\\S]*?(?=\\n\\n|$)/g, '').replace(/工具执行结果[\\s\\S]*?(?=\\n\\n|$)/g, '').replace(/🔧\\s*.*?\\n[\\s\\S]*?(?=\\n\\n|$)/g, '') // 清理工具图标开头的内容\n        .replace(/执行工具[\\s\\S]*?(?=\\n\\n|$)/g, '').replace(/Tool\\s+call[\\s\\S]*?(?=\\n\\n|$)/gi, '').replace(/\\n{3,}/g, '\\n\\n') // 清理多余的换行\n        .trim();\n        // 检查是否包含思考内容或正在思考中\n        const hasThinking = (0,_ThinkingMode__WEBPACK_IMPORTED_MODULE_3__.hasThinkingContent)(cleanContent);\n        const isCurrentlyThinking = showAIStatus && (aiState === null || aiState === void 0 ? void 0 : aiState.status) === 'thinking';\n        const isGeneratingWithThinking = showAIStatus && (aiState === null || aiState === void 0 ? void 0 : aiState.status) === 'generating' && hasThinking;\n        // 检测思考状态：不仅依赖aiState，也要检查内容中的思考标签\n        const hasThinkingInProgress = /<think>/.test(cleanContent) && !/<\\/think>/.test(cleanContent);\n        const isThinkingAnyway = hasThinkingInProgress || isCurrentlyThinking || isGeneratingWithThinking;\n        // 移除思考标签，获取清理后的内容用于显示\n        const contentWithoutThinking = (0,_ThinkingMode__WEBPACK_IMPORTED_MODULE_3__.removeThinkingContent)(cleanContent);\n        // 如果正在思考但还没有内容，仍然要显示思考面板\n        const shouldShow = contentWithoutThinking || isThinkingAnyway || hasThinking;\n        if (!shouldShow) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                (hasThinking || isThinkingAnyway) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThinkingMode__WEBPACK_IMPORTED_MODULE_3__.ThinkingMode, {\n                    content: cleanContent,\n                    isExpanded: isThinkingExpanded,\n                    onToggleExpand: ()=>setIsThinkingExpanded(!isThinkingExpanded),\n                    defaultHidden: false\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, this),\n                contentWithoutThinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0 animate-in fade-in-0 duration-300\",\n                    children: renderMarkdownContent(contentWithoutThinking)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    };\n    // 格式化时间（毫秒转秒）\n    const formatDuration = (nanoseconds)=>{\n        if (!nanoseconds) return null;\n        const seconds = (nanoseconds / 1000000000).toFixed(2);\n        return \"\".concat(seconds, \"s\");\n    };\n    const renderGenerationStatsIcon = ()=>{\n        // 根据是否有统计数据显示不同的悬浮内容\n        const statsText = message.total_duration ? \"生成时间: \".concat((message.total_duration / 1000000).toFixed(2), \"ms\\n\") + \"提示词处理: \".concat(message.prompt_eval_count || 0, \" tokens\\n\") + \"生成内容: \".concat(message.eval_count || 0, \" tokens\\n\") + \"提示词速度: \".concat(message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\\n\") + \"生成速度: \".concat(message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0, \" tokens/s\") : '正在生成中，统计信息将在完成后显示...';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative group\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-0 bottom-5 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 min-w-max\",\n                    children: statsText\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 237,\n            columnNumber: 7\n        }, this);\n    };\n    // 渲染生成统计信息\n    const renderGenerationStats = ()=>{\n        if (!isAssistant || !message.total_duration) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-2 text-xs text-gray-500 dark:text-gray-400 space-y-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"总时长: \",\n                                formatDuration(message.total_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"加载时长: \",\n                                formatDuration(message.load_duration)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"提示评估: \",\n                                message.prompt_eval_count,\n                                \" tokens (\",\n                                formatDuration(message.prompt_eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                \"生成: \",\n                                message.eval_count,\n                                \" tokens (\",\n                                formatDuration(message.eval_duration),\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n            lineNumber: 251,\n            columnNumber: 7\n        }, this);\n    };\n    // 对于助手消息，检查是否应该显示\n    if (isAssistant) {\n        const assistantContent = renderAssistantContent(message.content);\n        if (!assistantContent) {\n            return null; // 不渲染空的助手消息\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-3 \".concat(isUser ? 'justify-end' : 'justify-start'),\n        children: [\n            !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 \".concat(isTool ? 'bg-orange-600' : isToolCall ? 'bg-purple-600' : 'bg-blue-600'),\n                            children: isTool ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 17\n                            }, this) : isToolCall ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this),\n                        showAIStatus && aiState && aiState.status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AIStatusIndicator__WEBPACK_IMPORTED_MODULE_2__.AIStatusIndicator, {\n                                aiState: aiState\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col max-w-[70%]\",\n                children: [\n                    !isUser && isAssistant && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                children: message.model || '加载中...'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            renderGenerationStatsIcon()\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-lg px-4 py-2 transition-all duration-300 ease-in-out transform \".concat(isUser ? 'bg-theme-primary text-white shadow-md hover:shadow-lg' : isTool ? 'bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-200 border border-orange-300 dark:border-orange-700 shadow-sm hover:shadow-md' : isToolCall ? 'bg-theme-background-tertiary border border-theme-border text-theme-foreground shadow-sm hover:shadow-md' : 'bg-theme-card border border-theme-border text-theme-foreground shadow-sm hover:shadow-md animate-in slide-in-from-left-2 fade-in-0 duration-500'),\n                        children: isToolCall ? renderToolCallContent() : isTool ? renderToolContent(message.content) : isAssistant ? renderAssistantContent(message.content) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: renderMarkdownContent(message.content)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 12\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, this),\n            isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Info_User_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 text-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                    lineNumber: 330,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n                lineNumber: 329,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\chat\\\\components\\\\MessageItem.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageItem, \"3x2ATlWde6afHsaQQJRexlrfMGQ=\");\n_c = MessageItem;\nvar _c;\n$RefreshReg$(_c, \"MessageItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/components/MessageItem.tsx\n"));

/***/ })

});