"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _default = exports["default"] = {
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "padding": "0.5em",
    "color": "#000",
    "background": "#fff"
  },
  "hljs-subst": {
    "fontWeight": "normal",
    "color": "#000"
  },
  "hljs-title": {
    "fontWeight": "normal",
    "color": "#000"
  },
  "hljs-comment": {
    "color": "#808080",
    "fontStyle": "italic"
  },
  "hljs-quote": {
    "color": "#808080",
    "fontStyle": "italic"
  },
  "hljs-meta": {
    "color": "#808000"
  },
  "hljs-tag": {
    "background": "#efefef"
  },
  "hljs-section": {
    "fontWeight": "bold",
    "color": "#000080"
  },
  "hljs-name": {
    "fontWeight": "bold",
    "color": "#000080"
  },
  "hljs-literal": {
    "fontWeight": "bold",
    "color": "#000080"
  },
  "hljs-keyword": {
    "fontWeight": "bold",
    "color": "#000080"
  },
  "hljs-selector-tag": {
    "fontWeight": "bold",
    "color": "#000080"
  },
  "hljs-type": {
    "fontWeight": "bold",
    "color": "#000080"
  },
  "hljs-selector-id": {
    "fontWeight": "bold",
    "color": "#000080"
  },
  "hljs-selector-class": {
    "fontWeight": "bold",
    "color": "#000080"
  },
  "hljs-attribute": {
    "fontWeight": "bold",
    "color": "#0000ff"
  },
  "hljs-number": {
    "fontWeight": "normal",
    "color": "#0000ff"
  },
  "hljs-regexp": {
    "fontWeight": "normal",
    "color": "#0000ff"
  },
  "hljs-link": {
    "fontWeight": "normal",
    "color": "#0000ff"
  },
  "hljs-string": {
    "color": "#008000",
    "fontWeight": "bold"
  },
  "hljs-symbol": {
    "color": "#000",
    "background": "#d0eded",
    "fontStyle": "italic"
  },
  "hljs-bullet": {
    "color": "#000",
    "background": "#d0eded",
    "fontStyle": "italic"
  },
  "hljs-formula": {
    "color": "#000",
    "background": "#d0eded",
    "fontStyle": "italic"
  },
  "hljs-doctag": {
    "textDecoration": "underline"
  },
  "hljs-variable": {
    "color": "#660e7a"
  },
  "hljs-template-variable": {
    "color": "#660e7a"
  },
  "hljs-addition": {
    "background": "#baeeba"
  },
  "hljs-deletion": {
    "background": "#ffc8bd"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};