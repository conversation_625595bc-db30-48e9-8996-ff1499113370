"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/model-manager/page",{

/***/ "(app-pages-browser)/./src/app/model-manager/components/ModelDetailsModal.tsx":
/*!****************************************************************!*\
  !*** ./src/app/model-manager/components/ModelDetailsModal.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModelDetailsModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Settings,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Settings,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Info_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Info,Settings,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/model-manager/components/ModelLogo */ \"(app-pages-browser)/./src/app/model-manager/components/ModelLogo.tsx\");\n/* harmony import */ var _ModalWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ModalWrapper */ \"(app-pages-browser)/./src/app/model-manager/components/ModalWrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// 格式化文件大小\nconst formatFileSize = (bytes)=>{\n    if (bytes === 0) return '0 B';\n    const k = 1024;\n    const sizes = [\n        'B',\n        'KB',\n        'MB',\n        'GB',\n        'TB'\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\nconst InfoRow = (param)=>{\n    let { label, value, mono = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"py-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm font-medium text-theme-foreground-muted mb-2\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 33,\n                columnNumber: 5\n            }, undefined),\n            value ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-theme-foreground \".concat(mono ? 'font-mono text-sm' : '', \" \").concat(mono ? 'bg-theme-background-secondary px-4 py-3 rounded-xl text-xs break-all' : ''),\n                children: value\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-theme-foreground-muted text-sm italic\",\n                children: \"未设置\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined);\n};\n_c = InfoRow;\nconst Section = (param)=>{\n    let { title, children, icon: Icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-theme-background rounded-2xl p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold text-theme-foreground mb-4 flex items-center gap-3\",\n                children: [\n                    Icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-5 h-5 text-theme-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 16\n                    }, undefined),\n                    title\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 58,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-theme-border pt-4\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 62,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 57,\n        columnNumber: 3\n    }, undefined);\n};\n_c1 = Section;\nfunction ModelDetailsModal(param) {\n    let { model, onClose } = param;\n    if (!model) return null;\n    const modalIcon = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_model_manager_components_ModelLogo__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        modelName: model.family || model.base_model,\n        containerSize: 56,\n        imageSize: 32,\n        className: \"bg-theme-background-secondary rounded-2xl\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n    const headerContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-wrap gap-2 mt-3\",\n        children: model.tags && model.tags.length > 0 && model.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"px-3 py-1 bg-theme-primary/10 text-theme-primary text-xs rounded-full border border-theme-primary/20\",\n                children: tag\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModalWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        isOpen: true,\n        onClose: onClose,\n        title: model.display_name,\n        subtitle: model.base_model,\n        maxWidth: \"4xl\",\n        icon: modalIcon,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-8 pb-4\",\n                children: headerContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto px-8 pb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                            title: \"基本信息\",\n                            icon: _barrel_optimize_names_Info_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"模型别名\",\n                                                    value: model.display_name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"基础模型\",\n                                                    value: model.base_model\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"模型家族\",\n                                                    value: model.family\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"架构\",\n                                                    value: model.architecture || '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"参数规模\",\n                                                    value: model.parameter_count ? \"\".concat((model.parameter_count / 1e9).toFixed(1), \"B\") : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"文件大小\",\n                                                    value: model.size ? formatFileSize(model.size) : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"上下文长度\",\n                                                    value: model.context_length ? model.context_length.toLocaleString() : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"嵌入长度\",\n                                                    value: model.embedding_length ? model.embedding_length.toLocaleString() : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"量化级别\",\n                                                    value: model.quantization_level || '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"文件格式\",\n                                                    value: model.format || '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"模型能力\",\n                                                    value: model.capabilities && model.capabilities.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2\",\n                                                        children: model.capabilities.map((capability, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-3 py-1 bg-theme-primary/10 text-theme-primary text-xs rounded-full border border-theme-primary/20\",\n                                                                children: capability\n                                                            }, index, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 25\n                                                            }, void 0))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, void 0) : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"更新时间\",\n                                                    value: model.updated_at ? new Date(model.updated_at).toLocaleString('zh-CN') : new Date(model.created_at).toLocaleString('zh-CN')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                                    label: \"Ollama修改时间\",\n                                                    value: model.ollama_modified_at ? new Date(model.ollama_modified_at).toLocaleString('zh-CN') : '未知'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                        label: \"模型描述\",\n                                        value: model.description ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-theme-foreground overflow-hidden leading-relaxed\",\n                                            style: {\n                                                display: '-webkit-box',\n                                                WebkitLineClamp: 4,\n                                                WebkitBoxOrient: 'vertical',\n                                                minHeight: '1.5rem',\n                                                maxHeight: '6rem',\n                                                lineHeight: '1.5rem'\n                                            },\n                                            children: model.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, void 0) : null\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                            title: \"高级配置\",\n                            icon: _barrel_optimize_names_Info_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                        label: \"系统提示\",\n                                        value: model.system_prompt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"whitespace-pre-wrap text-sm bg-theme-background px-4 py-3 rounded-xl border border-theme-border overflow-x-auto\",\n                                            children: model.system_prompt\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 19\n                                        }, void 0) : null\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                        label: \"模型参数\",\n                                        value: model.parameters && Object.keys(model.parameters).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-theme-background px-4 py-3 rounded-xl border border-theme-border space-y-2\",\n                                            children: Object.entries(model.parameters).map((param)=>{\n                                                let [key, value] = param;\n                                                const displayValue = Array.isArray(value) ? value.join(', ') : String(value);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-theme-foreground-muted\",\n                                                            children: [\n                                                                key,\n                                                                \":\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 27\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-mono text-sm text-theme-foreground\",\n                                                            children: displayValue\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 27\n                                                        }, void 0)\n                                                    ]\n                                                }, key, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 25\n                                                }, void 0);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, void 0) : null\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                        label: \"模板\",\n                                        value: model.template ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"whitespace-pre-wrap text-sm bg-theme-background px-4 py-3 rounded-xl border border-theme-border overflow-x-auto font-mono\",\n                                            children: model.template\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, void 0) : null\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Section, {\n                            title: \"许可证\",\n                            icon: _barrel_optimize_names_Info_Settings_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InfoRow, {\n                                label: \"许可证信息\",\n                                value: model.license ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"whitespace-pre-wrap text-sm bg-theme-background px-4 py-3 rounded-xl border border-theme-border overflow-x-auto\",\n                                    children: model.license\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 17\n                                }, void 0) : null\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\model-manager\\\\components\\\\ModelDetailsModal.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ModelDetailsModal;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"InfoRow\");\n$RefreshReg$(_c1, \"Section\");\n$RefreshReg$(_c2, \"ModelDetailsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/model-manager/components/ModelDetailsModal.tsx\n"));

/***/ })

});