/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/custom-models/route";
exports.ids = ["app/api/custom-models/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-models%2Froute&page=%2Fapi%2Fcustom-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-models%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-models%2Froute&page=%2Fapi%2Fcustom-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-models%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_custom_models_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/custom-models/route.ts */ \"(rsc)/./src/app/api/custom-models/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/custom-models/route\",\n        pathname: \"/api/custom-models\",\n        filename: \"route\",\n        bundlePath: \"app/api/custom-models/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\api\\\\custom-models\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_custom_models_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-models%2Froute&page=%2Fapi%2Fcustom-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-models%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/custom-models/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/custom-models/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database_custom_models__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database/custom-models */ \"(rsc)/./src/lib/database/custom-models.ts\");\n/* harmony import */ var _lib_database_connection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database/connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _lib_ollama__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/ollama */ \"(rsc)/./src/lib/ollama.ts\");\n\n\n\n\n// 初始化数据库\n(0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_2__.initializeDatabase)();\nasync function GET(request) {\n    try {\n        // 同步Ollama模型\n        const ollamaClient = new _lib_ollama__WEBPACK_IMPORTED_MODULE_3__.OllamaClient();\n        const ollamaModels = await ollamaClient.getModels();\n        _lib_database_custom_models__WEBPACK_IMPORTED_MODULE_1__.CustomModelService.syncWithOllama(ollamaModels);\n        // 从数据库获取所有模型\n        const { searchParams } = new URL(request.url);\n        const search = searchParams.get('search') || undefined;\n        const tags = searchParams.get('tags')?.split(',') || undefined;\n        const sortBy = searchParams.get('sortBy') || 'ollama_modified_at';\n        const sortOrder = searchParams.get('sortOrder') || 'desc';\n        const models = _lib_database_custom_models__WEBPACK_IMPORTED_MODULE_1__.CustomModelService.getAll({\n            search,\n            tags,\n            sortBy,\n            sortOrder\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            models\n        });\n    } catch (error) {\n        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';\n        console.error('API Error in GET /api/custom-models:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const model = _lib_database_custom_models__WEBPACK_IMPORTED_MODULE_1__.CustomModelService.create(body);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            model\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('创建自定义模型失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '创建模型失败',\n            message: error instanceof Error ? error.message : '未知错误'\n        }, {\n            status: 400\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/custom-models/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connection.ts":
/*!****************************************!*\
  !*** ./src/lib/database/connection.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(better_sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// 数据库连接配置\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'chat.db');\nconst db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default())(dbPath);\n// 数据库初始化SQL\nconst initializeDatabase = ()=>{\n    // 先执行基础表创建（不包括custom_models表和相关索引）\n    const baseInitSQL = `\n    CREATE TABLE IF NOT EXISTS conversations (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      model TEXT NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    );\n\n    CREATE TABLE IF NOT EXISTS messages (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      conversation_id INTEGER NOT NULL,\n      role TEXT NOT NULL,\n      content TEXT NOT NULL,\n      model TEXT,\n      sequence_number INTEGER DEFAULT 0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      timestamp INTEGER, -- 毫秒级时间戳，用于精确排序\n      -- 工具调用相关字段\n      tool_name TEXT, -- 工具名称\n      tool_args TEXT, -- 工具参数 (JSON)\n      tool_result TEXT, -- 工具结果 (JSON)\n      tool_status TEXT CHECK (tool_status IN ('executing', 'completed', 'error')), -- 工具状态\n      tool_execution_time INTEGER, -- 工具执行时间(毫秒)\n      tool_error TEXT, -- 工具错误信息\n      -- Ollama生成统计信息\n      total_duration INTEGER,\n      load_duration INTEGER,\n      prompt_eval_count INTEGER,\n      prompt_eval_duration INTEGER,\n      eval_count INTEGER,\n      eval_duration INTEGER,\n      FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE\n    );\n\n    -- 自定义模型配置表（包含完整的Ollama API字段）\n    CREATE TABLE IF NOT EXISTS custom_models (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      base_model TEXT NOT NULL UNIQUE, -- 完整的基础模型名称\n      display_name TEXT NOT NULL, -- 用户可自定义的显示名称\n      model_hash TEXT NOT NULL UNIQUE, -- 内部使用的哈希名称\n      family TEXT NOT NULL, -- 模型家族信息\n      description TEXT,\n      system_prompt TEXT,\n      parameters TEXT, -- JSON格式存储所有参数\n      template TEXT, -- 自定义模板\n      license TEXT,\n      tags TEXT, -- JSON数组格式存储标签\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      size BIGINT,\n      digest TEXT,\n      ollama_modified_at TEXT,\n      -- Ollama API详细信息字段\n      architecture TEXT, -- 模型架构（llama、gemma等）\n      parameter_count INTEGER, -- 参数数量\n      context_length INTEGER, -- 上下文长度\n      embedding_length INTEGER, -- 嵌入维度\n      quantization_level TEXT, -- 量化级别（Q8_0、Q4_0等）\n      format TEXT, -- 文件格式（gguf等）\n      capabilities TEXT -- 模型能力（JSON数组格式：completion、vision等）\n    );\n\n    -- MCP服务器统一配置表\n    CREATE TABLE IF NOT EXISTS mcp_servers (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL UNIQUE,\n      display_name TEXT NOT NULL,\n      description TEXT,\n      type TEXT NOT NULL CHECK (type IN ('stdio', 'sse', 'streamable-http')),\n      status TEXT NOT NULL DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error', 'connecting')),\n      enabled BOOLEAN NOT NULL DEFAULT 1,\n      \n      -- STDIO配置\n      command TEXT,\n      args TEXT, -- JSON数组格式\n      working_directory TEXT,\n      \n      -- SSE/HTTP配置\n      url TEXT,\n      base_url TEXT,\n      port INTEGER,\n      path TEXT DEFAULT '/',\n      protocol TEXT DEFAULT 'http' CHECK (protocol IN ('http', 'https')),\n      \n      -- 通用配置\n      headers TEXT, -- JSON对象格式\n      auth_type TEXT CHECK (auth_type IN ('none', 'bearer', 'basic', 'api_key')),\n      auth_config TEXT, -- JSON格式\n      timeout_ms INTEGER DEFAULT 30000,\n      retry_attempts INTEGER DEFAULT 3,\n      retry_delay_ms INTEGER DEFAULT 1000,\n      \n      -- 扩展配置\n      extra_config TEXT, -- JSON格式，存储其他特殊配置\n      \n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      last_connected_at DATETIME,\n      error_message TEXT\n    );\n\n    -- MCP工具表\n    CREATE TABLE IF NOT EXISTS mcp_tools (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      server_id INTEGER NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT,\n      input_schema TEXT, -- JSON格式存储工具的输入参数模式\n      is_available BOOLEAN DEFAULT 1,\n      enabled BOOLEAN DEFAULT 1, -- 工具是否启用（在对话页面可见）\n      last_used_at DATETIME,\n      usage_count INTEGER DEFAULT 0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (server_id) REFERENCES mcp_servers (id) ON DELETE CASCADE,\n      UNIQUE(server_id, name)\n    );\n\n    -- 基础表索引\n    CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);\n    CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at DESC);\n    \n    -- 自定义模型相关索引\n    CREATE INDEX IF NOT EXISTS idx_custom_models_base_model ON custom_models(base_model);\n    CREATE INDEX IF NOT EXISTS idx_custom_models_hash ON custom_models(model_hash);\n    CREATE INDEX IF NOT EXISTS idx_custom_models_family ON custom_models(family);\n    \n    -- MCP相关索引\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_type ON mcp_servers(type);\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_status ON mcp_servers(status);\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_enabled ON mcp_servers(enabled);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_server_id ON mcp_tools(server_id);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_name ON mcp_tools(name);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_available ON mcp_tools(is_available);\n    -- 工具调用相关索引已迁移到messages表\n    CREATE INDEX IF NOT EXISTS idx_messages_tool_name ON messages(tool_name);\n    CREATE INDEX IF NOT EXISTS idx_messages_tool_status ON messages(tool_status);\n    CREATE INDEX IF NOT EXISTS idx_messages_conv_tool ON messages(conversation_id, tool_name);\n  `;\n    db.exec(baseInitSQL);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (db);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connection.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/custom-models.ts":
/*!*******************************************!*\
  !*** ./src/lib/database/custom-models.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomModelSchema: () => (/* binding */ CustomModelSchema),\n/* harmony export */   CustomModelService: () => (/* binding */ CustomModelService),\n/* harmony export */   ModelParametersSchema: () => (/* binding */ ModelParametersSchema)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ollama__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ollama */ \"(rsc)/./src/lib/ollama.ts\");\n\n\n\n\n// 模型参数验证模式\nconst ModelParametersSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    temperature: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).max(2).default(0.7),\n    top_p: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).max(1).default(0.9),\n    top_k: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).max(100).default(40),\n    repeat_penalty: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).default(1.1),\n    seed: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional(),\n    num_predict: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().default(-1),\n    num_ctx: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).default(4096),\n    num_thread: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(1).optional(),\n    num_gpu: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(1).optional(),\n    use_mmap: zod__WEBPACK_IMPORTED_MODULE_1__.z.boolean().optional(),\n    num_batch: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(1).optional(),\n    num_keep: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().min(0).optional(),\n    stop: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.string()).optional()\n});\n// 模型配置验证模式\nconst CustomModelSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().int(),\n    base_model: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n    display_name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n    model_hash: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n    description: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    family: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n    system_prompt: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    parameters: zod__WEBPACK_IMPORTED_MODULE_1__.z.record(zod__WEBPACK_IMPORTED_MODULE_1__.z.any()),\n    template: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    license: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    tags: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.string()).optional(),\n    created_at: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n    updated_at: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    size: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional(),\n    digest: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    ollama_modified_at: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    // 新增Ollama API信息\n    architecture: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    parameter_count: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional(),\n    context_length: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional(),\n    embedding_length: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional(),\n    quantization_level: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    format: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n    capabilities: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.string()).optional()\n});\nconst defaultParameters = {};\n// 检查名称冲突\nfunction checkNameConflict(baseModel, excludeId) {\n    const stmt = excludeId ? _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT COUNT(*) as count FROM custom_models WHERE base_model = ? AND id != ?') : _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT COUNT(*) as count FROM custom_models WHERE base_model = ?');\n    const result = excludeId ? stmt.get(baseModel, excludeId) : stmt.get(baseModel);\n    return result.count > 0;\n}\n// 检查哈希冲突\nfunction checkHashConflict(modelHash, excludeId) {\n    const stmt = excludeId ? _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT COUNT(*) as count FROM custom_models WHERE model_hash = ? AND id != ?') : _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT COUNT(*) as count FROM custom_models WHERE model_hash = ?');\n    const result = excludeId ? stmt.get(modelHash, excludeId) : stmt.get(modelHash);\n    return result.count > 0;\n}\nclass CustomModelService {\n    /**\r\n   * 创建自定义模型\r\n   */ static create(data) {\n        const validatedData = CustomModelSchema.omit({\n            id: true,\n            created_at: true,\n            updated_at: true\n        }).partial().merge(zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n            base_model: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n            display_name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n            family: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n            model_hash: zod__WEBPACK_IMPORTED_MODULE_1__.z.string(),\n            parameters: zod__WEBPACK_IMPORTED_MODULE_1__.z.record(zod__WEBPACK_IMPORTED_MODULE_1__.z.any())\n        })).parse(data);\n        const checkHashConflict = (hash)=>{\n            const row = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT id FROM custom_models WHERE model_hash = ?').get(hash);\n            return !!row;\n        };\n        let modelHash = validatedData.model_hash;\n        if (checkHashConflict(modelHash)) {\n            let attempts = 0;\n            let uniqueHashFound = false;\n            while(attempts < 10 && !uniqueHashFound){\n                const newHash = this.generateModelHash(`${validatedData.base_model}-${attempts}`);\n                if (!checkHashConflict(newHash)) {\n                    modelHash = newHash;\n                    uniqueHashFound = true;\n                }\n                attempts++;\n            }\n            if (!uniqueHashFound) {\n                throw new Error(\"Failed to generate a unique model hash after 10 attempts.\");\n            }\n        }\n        const parameters = JSON.stringify(validatedData.parameters);\n        const tags = JSON.stringify(validatedData.tags || []);\n        const stmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('INSERT INTO custom_models (base_model, display_name, model_hash, description, family, system_prompt, parameters, tags, template, license, size, digest, ollama_modified_at, architecture, parameter_count, context_length, embedding_length, quantization_level, format, capabilities) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');\n        const capabilities = JSON.stringify(validatedData.capabilities || []);\n        const result = stmt.run(validatedData.base_model, validatedData.display_name, modelHash, validatedData.description, validatedData.family, validatedData.system_prompt, parameters, tags, validatedData.template, validatedData.license, validatedData.size, validatedData.digest, validatedData.ollama_modified_at, validatedData.architecture, validatedData.parameter_count, validatedData.context_length, validatedData.embedding_length, validatedData.quantization_level, validatedData.format, capabilities);\n        const newModelId = result.lastInsertRowid;\n        const newModel = this.getById(Number(newModelId));\n        if (!newModel) {\n            throw new Error('Failed to create or retrieve the new model.');\n        }\n        return newModel;\n    }\n    /**\r\n   * 更新自定义模型\r\n   */ static update(id, data) {\n        const updateData = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n            display_name: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().min(1, '显示名称不能为空'),\n            description: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional(),\n            tags: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(zod__WEBPACK_IMPORTED_MODULE_1__.z.string()).optional()\n        }).partial().parse(data);\n        const fields = Object.keys(updateData);\n        if (fields.length === 0) return false;\n        const updateFields = fields.map((field)=>`${field} = ?`);\n        const values = fields.map((field)=>{\n            const value = updateData[field];\n            return field === 'tags' ? JSON.stringify(value) : value;\n        });\n        const stmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`UPDATE custom_models SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`);\n        const result = stmt.run(...values, id);\n        return result.changes > 0;\n    }\n    /**\r\n   * 删除自定义模型\r\n   */ static delete(id) {\n        const stmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('DELETE FROM custom_models WHERE id = ?');\n        const result = stmt.run(id);\n        return result.changes > 0;\n    }\n    /**\r\n   * 根据ID获取模型\r\n   */ static getById(id) {\n        const row = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare('SELECT * FROM custom_models WHERE id = ?').get(id);\n        if (row) {\n            return this.mapRowToModel(row);\n        }\n        return null;\n    }\n    /**\r\n   * 获取所有模型\r\n   */ static getAll({ search, tags, sortBy, sortOrder }) {\n        let query = 'SELECT * FROM custom_models WHERE 1=1';\n        const params = [];\n        if (search) {\n            query += ' AND (base_model LIKE ? OR display_name LIKE ? OR description LIKE ?)';\n            params.push(`%${search}%`, `%${search}%`, `%${search}%`);\n        }\n        if (tags && tags.length > 0) {\n            query += ` AND (${tags.map(()=>\"json_each.value = ?\").join(' OR ')})`;\n            query = `SELECT t1.* FROM custom_models t1, json_each(t1.tags) WHERE t1.id IN (SELECT t1.id FROM custom_models t1, json_each(t1.tags) WHERE ${tags.map(()=>`json_each.value LIKE ?`).join(' OR ')})`;\n            params.push(...tags.map((t)=>`%${t}%`));\n        }\n        if (sortBy && sortOrder) {\n            const sortColumn = [\n                'base_model',\n                'created_at',\n                'updated_at',\n                'ollama_modified_at'\n            ].includes(sortBy) ? sortBy : 'base_model';\n            const order = sortOrder.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';\n            query += ` ORDER BY ${sortColumn} ${order}`;\n        }\n        const rows = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(query).all(params);\n        return rows.map(this.mapRowToModel);\n    }\n    static getTags() {\n        const rows = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(\"SELECT DISTINCT value FROM custom_models, json_each(tags)\").all();\n        return rows.map((row)=>row.value);\n    }\n    static async syncWithOllama(ollamaModels) {\n        const dbModels = this.getAll({});\n        const dbModelMap = new Map(dbModels.map((m)=>[\n                m.base_model,\n                m\n            ]));\n        const ollamaModelMap = new Map(ollamaModels.map((m)=>[\n                m.name,\n                m\n            ]));\n        const ollamaClient = new _ollama__WEBPACK_IMPORTED_MODULE_3__.OllamaClient();\n        // 1. 在事务之外，首先获取所有模型的详细信息\n        const detailedOllamaModels = await Promise.all(ollamaModels.map(async (model)=>{\n            try {\n                const showInfo = await ollamaClient.getModelDetails(model.name);\n                return {\n                    ...model,\n                    showInfo,\n                    success: true\n                };\n            } catch (error) {\n                console.error(`获取模型 ${model.name} 的详细信息失败:`, error);\n                return {\n                    ...model,\n                    showInfo: null,\n                    success: false\n                };\n            }\n        }));\n        const syncTransaction = _connection__WEBPACK_IMPORTED_MODULE_0__.db.transaction(()=>{\n            // 2. 在同步事务中处理数据库操作\n            for (const detailedModel of detailedOllamaModels){\n                if (!detailedModel.success || !detailedModel.showInfo) {\n                    continue; // 如果获取详情失败则跳过\n                }\n                const existingDbModel = dbModelMap.get(detailedModel.name);\n                const modelData = {\n                    base_model: detailedModel.name,\n                    family: detailedModel.showInfo.details.family || 'unknown',\n                    size: detailedModel.size,\n                    digest: detailedModel.digest,\n                    ollama_modified_at: detailedModel.modified_at,\n                    template: detailedModel.showInfo.template,\n                    system_prompt: detailedModel.showInfo.system,\n                    license: detailedModel.showInfo.license\n                };\n                // 解析和设置参数\n                if (detailedModel.showInfo.parameters) {\n                    try {\n                        const params = this.parseOllamaParameters(detailedModel.showInfo.parameters);\n                        modelData.parameters = params;\n                    } catch (e) {\n                        console.warn(`无法解析模型 '${detailedModel.name}' 的参数:`, e);\n                    }\n                }\n                // 解析模型架构和详细信息\n                if (detailedModel.showInfo.model_info) {\n                    const modelInfo = detailedModel.showInfo.model_info;\n                    modelData.architecture = modelInfo['general.architecture'];\n                    modelData.parameter_count = modelInfo['general.parameter_count'];\n                    // 动态查找上下文长度（支持不同架构）\n                    modelData.context_length = this.findModelInfoValue(modelInfo, 'context_length');\n                    // 动态查找嵌入长度\n                    modelData.embedding_length = this.findModelInfoValue(modelInfo, 'embedding_length');\n                }\n                // 设置格式和量化信息\n                if (detailedModel.showInfo.details) {\n                    modelData.format = detailedModel.showInfo.details.format;\n                    modelData.quantization_level = detailedModel.showInfo.details.quantization_level;\n                }\n                // 设置能力信息\n                if (detailedModel.showInfo.capabilities) {\n                    modelData.capabilities = detailedModel.showInfo.capabilities;\n                }\n                if (existingDbModel) {\n                    const updatedParameters = {\n                        ...existingDbModel.parameters,\n                        ...modelData.parameters || {}\n                    };\n                    const { display_name, description, tags, ...ollamaData } = modelData;\n                    this._updateOllamaData(existingDbModel.id, {\n                        ...ollamaData,\n                        parameters: updatedParameters\n                    });\n                } else {\n                    // Create new model\n                    const modelHash = this.generateModelHash(detailedModel.name);\n                    const displayName = detailedModel.name.split(':')[0];\n                    const parameters = modelData.parameters || defaultParameters;\n                    const fullModelData = {\n                        base_model: detailedModel.name,\n                        display_name: displayName,\n                        model_hash: modelHash,\n                        family: detailedModel.showInfo.details.family || 'unknown',\n                        description: '',\n                        system_prompt: modelData.system_prompt || '',\n                        parameters,\n                        tags: [],\n                        template: modelData.template,\n                        license: modelData.license,\n                        size: detailedModel.size,\n                        digest: detailedModel.digest,\n                        ollama_modified_at: detailedModel.modified_at,\n                        // 新增的Ollama API字段\n                        architecture: modelData.architecture,\n                        parameter_count: modelData.parameter_count,\n                        context_length: modelData.context_length,\n                        embedding_length: modelData.embedding_length,\n                        quantization_level: modelData.quantization_level,\n                        format: modelData.format,\n                        capabilities: modelData.capabilities || []\n                    };\n                    this.create(fullModelData);\n                }\n            }\n            // 3. 如果模型在Ollama中不再存在，则从我们的数据库中删除\n            for (const dbModel of dbModels){\n                if (!ollamaModelMap.has(dbModel.base_model)) {\n                    this.delete(dbModel.id);\n                }\n            }\n        });\n        syncTransaction();\n    }\n    /**\r\n   * 内部方法：只更新从Ollama同步的数据\r\n   */ static _updateOllamaData(id, data) {\n        const fields = Object.keys(data);\n        if (fields.length === 0) return false;\n        const updateFields = fields.map((field)=>`${field} = ?`);\n        const values = fields.map((field)=>{\n            const value = data[field];\n            if (field === 'parameters' || field === 'capabilities') return JSON.stringify(value);\n            return value;\n        });\n        const stmt = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`UPDATE custom_models SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`);\n        const result = stmt.run(...values, id);\n        return result.changes > 0;\n    }\n    static mapRowToModel(row) {\n        const model = {\n            id: row.id,\n            base_model: row.base_model,\n            display_name: row.display_name,\n            model_hash: row.model_hash,\n            description: row.description,\n            family: row.family,\n            system_prompt: row.system_prompt,\n            parameters: row.parameters ? JSON.parse(row.parameters) : {},\n            tags: row.tags ? JSON.parse(row.tags) : [],\n            created_at: row.created_at,\n            updated_at: row.updated_at,\n            size: row.size,\n            digest: row.digest,\n            ollama_modified_at: row.ollama_modified_at,\n            template: row.template,\n            license: row.license,\n            // 新增的Ollama API字段\n            architecture: row.architecture,\n            parameter_count: row.parameter_count,\n            context_length: row.context_length,\n            embedding_length: row.embedding_length,\n            quantization_level: row.quantization_level,\n            format: row.format,\n            capabilities: row.capabilities ? JSON.parse(row.capabilities) : []\n        };\n        return model;\n    }\n    static generateModelHash(name) {\n        return crypto__WEBPACK_IMPORTED_MODULE_2___default().createHash('sha256').update(name).digest('hex').substring(0, 16);\n    }\n    /**\r\n   * 动态查找model_info中的字段值\r\n   * 支持不同架构的字段命名格式\r\n   */ static findModelInfoValue(modelInfo, fieldSuffix) {\n        // 查找所有包含指定后缀的字段\n        const matchingKeys = Object.keys(modelInfo).filter((key)=>key.endsWith('.' + fieldSuffix) || key.endsWith('_' + fieldSuffix));\n        if (matchingKeys.length === 0) return undefined;\n        // 优先返回第一个匹配的值\n        const firstKey = matchingKeys[0];\n        const value = modelInfo[firstKey];\n        return typeof value === 'number' ? value : undefined;\n    }\n    /**\r\n   * 解析Ollama parameters字符串为对象\r\n   */ static parseOllamaParameters(parametersStr) {\n        const params = {};\n        if (!parametersStr) return params;\n        const lines = parametersStr.split('\\n');\n        lines.forEach((line)=>{\n            const trimmedLine = line.trim();\n            if (!trimmedLine) return;\n            // 解析格式: \"param_name    value\"\n            const match = trimmedLine.match(/^(\\w+)\\s+(.+)$/);\n            if (!match) return;\n            const [, key, valueStr] = match;\n            // 解析值\n            let value = valueStr.trim();\n            // 去除引号\n            if (value.startsWith('\"') && value.endsWith('\"')) {\n                value = value.slice(1, -1);\n            }\n            // 特殊处理stop参数（可能有多个）\n            if (key === 'stop') {\n                if (!params.stop) {\n                    params.stop = [];\n                }\n                params.stop.push(value);\n            } else {\n                // 类型转换\n                if (value.toLowerCase() === 'true') {\n                    params[key] = true;\n                } else if (value.toLowerCase() === 'false') {\n                    params[key] = false;\n                } else if (!isNaN(Number(value)) && value.trim() !== '') {\n                    params[key] = Number(value);\n                } else {\n                    params[key] = value;\n                }\n            }\n        });\n        return params;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/custom-models.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaClient: () => (/* binding */ OllamaClient),\n/* harmony export */   ollamaClient: () => (/* binding */ ollamaClient)\n/* harmony export */ });\n// Ollama API 客户端\nconst OLLAMA_BASE_URL = 'http://localhost:11434';\nclass OllamaClient {\n    constructor(baseUrl = OLLAMA_BASE_URL){\n        this.baseUrl = baseUrl;\n    }\n    /**\n   * 获取本地可用的模型列表\n   */ async getModels() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            const data = await response.json();\n            return data.models || [];\n        } catch (error) {\n            console.error('获取模型列表失败:', error);\n            throw new Error('无法连接到Ollama服务，请确保Ollama正在运行');\n        }\n    }\n    /**\n   * 获取指定模型的详细信息\n   */ async getModelDetails(modelName) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/show`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: modelName\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(`获取模型 '${modelName}' 详细信息失败:`, response.status, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            const details = await response.json();\n            // 改进的系统提示词提取逻辑\n            let systemPrompt = '';\n            if (details.modelfile) {\n                // 尝试多种 SYSTEM 指令格式（不区分大小写）\n                const patterns = [\n                    // 三引号格式：SYSTEM \"\"\"content\"\"\"\n                    /(?:SYSTEM|system)\\s+\"\"\"([\\s\\S]*?)\"\"\"/i,\n                    // 双引号格式：SYSTEM \"content\"\n                    /(?:SYSTEM|system)\\s+\"([^\"]*?)\"/i,\n                    // 单引号格式：SYSTEM 'content'\n                    /(?:SYSTEM|system)\\s+'([^']*?)'/i,\n                    // 无引号格式（到行尾）：SYSTEM content\n                    /(?:SYSTEM|system)\\s+([^\\n\\r]*)/i\n                ];\n                for (const pattern of patterns){\n                    const match = details.modelfile.match(pattern);\n                    if (match && match[1].trim()) {\n                        systemPrompt = match[1].trim();\n                        break;\n                    }\n                }\n            }\n            details.system = systemPrompt;\n            return details;\n        } catch (error) {\n            console.error(`请求模型 '${modelName}' 详细信息时出错:`, error);\n            throw new Error(`无法获取模型 '${modelName}' 的详细信息`);\n        }\n    }\n    /**\n   * 发送聊天请求（非流式）\n   */ async chat(request) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/chat`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...request,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('聊天请求失败:', error);\n            throw new Error('聊天请求失败，请检查网络连接和Ollama服务状态');\n        }\n    }\n    /**\n   * 发送流式聊天请求\n   */ async *chatStream(request) {\n        try {\n            // console.log('Ollama chatStream 请求:', JSON.stringify(request, null, 2));\n            const response = await fetch(`${this.baseUrl}/api/chat`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...request,\n                    stream: true\n                })\n            });\n            // console.log('Ollama 响应状态:', response.status, response.statusText);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            if (!response.body) {\n                throw new Error('响应体为空');\n            }\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let buffer = '';\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    buffer += decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = buffer.split('\\n');\n                    // 保留最后一行（可能不完整）\n                    buffer = lines.pop() || '';\n                    for (const line of lines){\n                        const trimmedLine = line.trim();\n                        if (trimmedLine) {\n                            try {\n                                const data = JSON.parse(trimmedLine);\n                                yield data;\n                                // 如果收到完成标志，结束生成\n                                if (data.done) {\n                                    return;\n                                }\n                            } catch (parseError) {\n                                console.warn('解析JSON失败:', parseError, '原始数据:', trimmedLine);\n                            }\n                        }\n                    }\n                }\n                // 处理缓冲区中剩余的数据\n                if (buffer.trim()) {\n                    try {\n                        const data = JSON.parse(buffer.trim());\n                        yield data;\n                    } catch (parseError) {\n                        console.warn('解析最后的JSON失败:', parseError);\n                    }\n                }\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            console.error('流式聊天请求失败:', error);\n            if (error instanceof Error) {\n                throw error; // 保持原始错误信息\n            } else {\n                throw new Error('流式聊天请求失败，请检查网络连接和Ollama服务状态');\n            }\n        }\n    }\n    /**\n   * 检查Ollama服务是否可用\n   */ async isAvailable() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`, {\n                method: 'GET',\n                signal: AbortSignal.timeout(5000)\n            });\n            return response.ok;\n        } catch  {\n            return false;\n        }\n    }\n    /**\n   * 检查指定模型是否已加载到内存中\n   */ async isModelLoaded(modelName) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/ps`, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                console.error('获取模型状态失败:', response.status, response.statusText);\n                return false;\n            }\n            const data = await response.json();\n            const loadedModels = data.models || [];\n            // 检查指定模型是否在已加载的模型列表中\n            return loadedModels.some((model)=>model.name === modelName);\n        } catch (error) {\n            console.error('检查模型加载状态失败:', error);\n            return false;\n        }\n    }\n    /**\n   * 格式化模型大小\n   */ static formatModelSize(bytes) {\n        const units = [\n            'B',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        let size = bytes;\n        let unitIndex = 0;\n        while(size >= 1024 && unitIndex < units.length - 1){\n            size /= 1024;\n            unitIndex++;\n        }\n        return `${size.toFixed(1)} ${units[unitIndex]}`;\n    }\n    /**\n   * 格式化模型名称（移除标签）\n   */ static formatModelName(name) {\n        return name.split(':')[0];\n    }\n}\n// 默认客户端实例\nconst ollamaClient = new OllamaClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ollama.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcustom-models%2Froute&page=%2Fapi%2Fcustom-models%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcustom-models%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();